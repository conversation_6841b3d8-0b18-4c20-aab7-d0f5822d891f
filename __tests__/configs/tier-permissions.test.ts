/**
 * Comprehensive unit tests for tier-based permission system
 * Tests all permission flags to ensure correct behavior and prevent migration issues
 */

import { SubscriptionTier } from '@prisma/client'
import {
  getTierPermissions,
  getAccessPermissions,
  getResourceLimits,
  TierAccess,
  hasPaidFeatures,
} from '@/app/configs/tier-permissions'

describe('Tier Permission System', () => {
  describe('Permission Flag Tests', () => {
    describe('canEditLanguagePreference', () => {
      it('should be false for VIEWER tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.VIEWER)
        expect(permissions.canEditLanguagePreference).toBe(false)
      })

      it('should be false for DUMMY tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.DUMMY)
        expect(permissions.canEditLanguagePreference).toBe(false)
      })

      it('should be true for FREE tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.FREE)
        expect(permissions.canEditLanguagePreference).toBe(true)
      })

      it('should be true for PRO tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.PRO)
        expect(permissions.canEditLanguagePreference).toBe(true)
      })

      it('should be true for GUEST tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.GUEST)
        expect(permissions.canEditLanguagePreference).toBe(true)
      })

      it('should be true for ULTRA tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.ULTRA)
        expect(permissions.canEditLanguagePreference).toBe(true)
      })

      it('should be true for BUSINESS tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.BUSINESS)
        expect(permissions.canEditLanguagePreference).toBe(true)
      })
    })

    describe('canExportDiagram (canCreateDiagramExport)', () => {
      it('should be false for VIEWER tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.VIEWER)
        expect(permissions.canExportDiagram).toBe(false)
      })

      it('should be false for FREE tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.FREE)
        expect(permissions.canExportDiagram).toBe(false)
      })

      it('should be false for DUMMY tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.DUMMY)
        expect(permissions.canExportDiagram).toBe(false)
      })

      it('should be true for PRO tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.PRO)
        expect(permissions.canExportDiagram).toBe(true)
      })

      it('should be true for GUEST tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.GUEST)
        expect(permissions.canExportDiagram).toBe(true)
      })

      it('should be true for ULTRA tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.ULTRA)
        expect(permissions.canExportDiagram).toBe(true)
      })

      it('should be true for BUSINESS tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.BUSINESS)
        expect(permissions.canExportDiagram).toBe(true)
      })
    })

    describe('quickResearch (canCreateQuickResearch)', () => {
      it('should be false for VIEWER tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.VIEWER)
        expect(permissions.quickResearch).toBe(false)
      })

      it('should be true for FREE tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.FREE)
        expect(permissions.quickResearch).toBe(true)
      })

      it('should be true for DUMMY tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.DUMMY)
        expect(permissions.quickResearch).toBe(true)
      })

      it('should be true for PRO tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.PRO)
        expect(permissions.quickResearch).toBe(true)
      })

      it('should be true for GUEST tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.GUEST)
        expect(permissions.quickResearch).toBe(true)
      })

      it('should be true for ULTRA tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.ULTRA)
        expect(permissions.quickResearch).toBe(true)
      })

      it('should be true for BUSINESS tier', () => {
        const permissions = getTierPermissions(SubscriptionTier.BUSINESS)
        expect(permissions.quickResearch).toBe(true)
      })
    })
  })

  describe('Access Permission Direct Tests', () => {
    describe('canEditLanguagePreference direct access', () => {
      it('should match expected values for all tiers', () => {
        const expectedValues = {
          [SubscriptionTier.VIEWER]: false,
          [SubscriptionTier.FREE]: true,
          [SubscriptionTier.DUMMY]: false,
          [SubscriptionTier.PRO]: true,
          [SubscriptionTier.GUEST]: true,
          [SubscriptionTier.ULTRA]: true,
          [SubscriptionTier.BUSINESS]: true,
        }

        Object.entries(expectedValues).forEach(([tier, expected]) => {
          const permissions = getAccessPermissions(tier as SubscriptionTier)
          expect(permissions.canEditLanguagePreference).toBe(expected)
        })
      })
    })

    describe('canCreateDiagramExport direct access', () => {
      it('should match expected values for all tiers', () => {
        const expectedValues = {
          [SubscriptionTier.VIEWER]: false,
          [SubscriptionTier.FREE]: false,
          [SubscriptionTier.DUMMY]: false,
          [SubscriptionTier.PRO]: true,
          [SubscriptionTier.GUEST]: true,
          [SubscriptionTier.ULTRA]: true,
          [SubscriptionTier.BUSINESS]: true,
        }

        Object.entries(expectedValues).forEach(([tier, expected]) => {
          const permissions = getAccessPermissions(tier as SubscriptionTier)
          expect(permissions.canCreateDiagramExport).toBe(expected)
        })
      })
    })

    describe('canCreateQuickResearch direct access', () => {
      it('should match expected values for all tiers', () => {
        const expectedValues = {
          [SubscriptionTier.VIEWER]: false,
          [SubscriptionTier.FREE]: true,
          [SubscriptionTier.DUMMY]: true,
          [SubscriptionTier.PRO]: true,
          [SubscriptionTier.GUEST]: true,
          [SubscriptionTier.ULTRA]: true,
          [SubscriptionTier.BUSINESS]: true,
        }

        Object.entries(expectedValues).forEach(([tier, expected]) => {
          const permissions = getAccessPermissions(tier as SubscriptionTier)
          expect(permissions.canCreateQuickResearch).toBe(expected)
        })
      })
    })
  })

  describe('Legacy Compatibility Tests', () => {
    it('should maintain backward compatibility for all permission mappings', () => {
      Object.values(SubscriptionTier).forEach(tier => {
        const legacyPermissions = getTierPermissions(tier)
        const directPermissions = getAccessPermissions(tier)

        // Test mapping consistency
        expect(legacyPermissions.quickResearch).toBe(
          directPermissions.canCreateQuickResearch
        )
        expect(legacyPermissions.canExportDiagram).toBe(
          directPermissions.canCreateDiagramExport
        )
        expect(legacyPermissions.canEditLanguagePreference).toBe(
          directPermissions.canEditLanguagePreference
        )
      })
    })
  })

  describe('Tier Configuration Validation', () => {
    it('should have all required permission flags for each tier', () => {
      const requiredPermissions = [
        'canEditDragTreeTitle',
        'canCreateDragTree',
        'canEditDragTreeNodeTitle',
        'canDeleteDragTreeNode',
        'canCreateDragTreeNode',
        'canCreateAiChat',
        'canCreateAiMessage',
        'canDeleteAiChat',
        'canCreateAiGeneration',
        'canEditAiGenerationTitle',
        'canEditAiGenerationContent',
        'canDeleteAiGeneration',
        'canCreateQuickResearch',
        'canCreateDiagramExport',
        'canDeleteAsset',
        'canEditLanguagePreference',
      ]

      Object.values(SubscriptionTier).forEach(tier => {
        const permissions = getAccessPermissions(tier)
        requiredPermissions.forEach(permission => {
          expect(permissions).toHaveProperty(permission)
          expect(
            typeof permissions[permission as keyof typeof permissions]
          ).toBe('boolean')
        })
      })
    })

    it('should have consistent tier access structure', () => {
      Object.values(SubscriptionTier).forEach(tier => {
        const tierConfig = TierAccess[tier]
        expect(tierConfig).toHaveProperty('resourceLimits')
        expect(tierConfig).toHaveProperty('accessPermissions')
        expect(typeof tierConfig.resourceLimits).toBe('object')
        expect(typeof tierConfig.accessPermissions).toBe('object')
      })
    })
  })

  describe('Business Logic Tests', () => {
    it('should correctly identify paid tiers', () => {
      expect(hasPaidFeatures(SubscriptionTier.VIEWER)).toBe(false)
      expect(hasPaidFeatures(SubscriptionTier.FREE)).toBe(false)
      expect(hasPaidFeatures(SubscriptionTier.DUMMY)).toBe(false)
      expect(hasPaidFeatures(SubscriptionTier.PRO)).toBe(true)
      expect(hasPaidFeatures(SubscriptionTier.GUEST)).toBe(true)
      expect(hasPaidFeatures(SubscriptionTier.ULTRA)).toBe(true)
      expect(hasPaidFeatures(SubscriptionTier.BUSINESS)).toBe(true)
    })

    it('should have restrictive permissions for VIEWER tier', () => {
      const permissions = getAccessPermissions(SubscriptionTier.VIEWER)

      // VIEWER should have most permissions set to false
      expect(permissions.canEditDragTreeTitle).toBe(false)
      expect(permissions.canCreateDragTree).toBe(false)
      expect(permissions.canCreateAiChat).toBe(false)
      expect(permissions.canCreateQuickResearch).toBe(false)
      expect(permissions.canCreateDiagramExport).toBe(false)
      expect(permissions.canEditLanguagePreference).toBe(false)
    })

    it('should have restrictive permissions for DUMMY tier', () => {
      const permissions = getAccessPermissions(SubscriptionTier.DUMMY)

      // DUMMY should have most editing permissions set to false
      expect(permissions.canEditDragTreeTitle).toBe(false)
      expect(permissions.canCreateDragTree).toBe(false)
      expect(permissions.canCreateAiChat).toBe(false)
      expect(permissions.canCreateDiagramExport).toBe(false)
      expect(permissions.canEditLanguagePreference).toBe(false)

      // But should allow research
      expect(permissions.canCreateQuickResearch).toBe(true)
    })
  })

  describe('Permission Migration Safety Tests', () => {
    it('should prevent accidental permission changes during migrations', () => {
      // This test serves as a safety net to catch unintended permission changes
      const expectedPermissionSnapshot = {
        [SubscriptionTier.VIEWER]: {
          canEditLanguagePreference: false,
          canCreateQuickResearch: false,
          canCreateDiagramExport: false,
        },
        [SubscriptionTier.FREE]: {
          canEditLanguagePreference: true,
          canCreateQuickResearch: true,
          canCreateDiagramExport: false,
        },
        [SubscriptionTier.DUMMY]: {
          canEditLanguagePreference: false,
          canCreateQuickResearch: true,
          canCreateDiagramExport: false,
        },
        [SubscriptionTier.PRO]: {
          canEditLanguagePreference: true,
          canCreateQuickResearch: true,
          canCreateDiagramExport: true,
        },
        [SubscriptionTier.GUEST]: {
          canEditLanguagePreference: true,
          canCreateQuickResearch: true,
          canCreateDiagramExport: true,
        },
        [SubscriptionTier.ULTRA]: {
          canEditLanguagePreference: true,
          canCreateQuickResearch: true,
          canCreateDiagramExport: true,
        },
        [SubscriptionTier.BUSINESS]: {
          canEditLanguagePreference: true,
          canCreateQuickResearch: true,
          canCreateDiagramExport: true,
        },
      }

      Object.entries(expectedPermissionSnapshot).forEach(
        ([tier, expectedPerms]) => {
          const actualPermissions = getAccessPermissions(
            tier as SubscriptionTier
          )

          expect(actualPermissions.canEditLanguagePreference).toBe(
            expectedPerms.canEditLanguagePreference
          )
          expect(actualPermissions.canCreateQuickResearch).toBe(
            expectedPerms.canCreateQuickResearch
          )
          expect(actualPermissions.canCreateDiagramExport).toBe(
            expectedPerms.canCreateDiagramExport
          )
        }
      )
    })

    it('should maintain legacy compatibility mapping integrity', () => {
      // Ensure legacy getTierPermissions function maps correctly
      Object.values(SubscriptionTier).forEach(tier => {
        const legacy = getTierPermissions(tier)
        const direct = getAccessPermissions(tier)

        // Critical mappings that components depend on
        expect(legacy.quickResearch).toBe(direct.canCreateQuickResearch)
        expect(legacy.canExportDiagram).toBe(direct.canCreateDiagramExport)
        expect(legacy.canEditLanguagePreference).toBe(
          direct.canEditLanguagePreference
        )

        // Ensure all legacy properties exist
        expect(legacy).toHaveProperty('quickResearch')
        expect(legacy).toHaveProperty('canExportDiagram')
        expect(legacy).toHaveProperty('canEditLanguagePreference')
        expect(legacy).toHaveProperty('maxDragTrees')
        expect(legacy).toHaveProperty('aiPaneGeneratePerTree')
        expect(legacy).toHaveProperty('aiPaneChatPerTree')
        expect(legacy).toHaveProperty('maxBatchResearchItems')
      })
    })
  })

  describe('Component Integration Tests', () => {
    it('should provide correct permissions for ResearchEditor component', () => {
      // Test that VIEWER tier makes research read-only
      const viewerPermissions = getTierPermissions(SubscriptionTier.VIEWER)
      expect(viewerPermissions.quickResearch).toBe(false) // Should make editor read-only

      // Test that other tiers allow research
      const freePermissions = getTierPermissions(SubscriptionTier.FREE)
      expect(freePermissions.quickResearch).toBe(true) // Should allow editing
    })

    it('should provide correct permissions for FlowExportButton component', () => {
      // Test that lower tiers cannot export
      const viewerPermissions = getTierPermissions(SubscriptionTier.VIEWER)
      const freePermissions = getTierPermissions(SubscriptionTier.FREE)
      expect(viewerPermissions.canExportDiagram).toBe(false)
      expect(freePermissions.canExportDiagram).toBe(false)

      // Test that higher tiers can export
      const proPermissions = getTierPermissions(SubscriptionTier.PRO)
      expect(proPermissions.canExportDiagram).toBe(true)
    })

    it('should provide correct permissions for language preference components', () => {
      // Test that VIEWER and DUMMY cannot edit language preferences
      const viewerPermissions = getTierPermissions(SubscriptionTier.VIEWER)
      const dummyPermissions = getTierPermissions(SubscriptionTier.DUMMY)
      expect(viewerPermissions.canEditLanguagePreference).toBe(false)
      expect(dummyPermissions.canEditLanguagePreference).toBe(false)

      // Test that other tiers can edit language preferences
      const freePermissions = getTierPermissions(SubscriptionTier.FREE)
      const proPermissions = getTierPermissions(SubscriptionTier.PRO)
      expect(freePermissions.canEditLanguagePreference).toBe(true)
      expect(proPermissions.canEditLanguagePreference).toBe(true)
    })
  })
})
