/**
 * Tests for permission-based UI components
 * Ensures correct behavior based on tier permissions
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { SubscriptionTier } from '@prisma/client'

// Mock next-auth
const mockSession = (tier: SubscriptionTier) => ({
  data: { user: { subscription: { tier } } },
})

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}))

// Mock stores
jest.mock('@/app/stores/dragtree_store', () => ({
  useDragTreeStore: jest.fn(() => ({
    preferredLanguage: 'en',
    setPreferredLanguage: jest.fn(),
  })),
}))

jest.mock('@/app/stores/ui_store', () => ({
  useUIStore: jest.fn(() => true),
}))

jest.mock('@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore', () => ({
  useTabStore: jest.fn(() => ({
    activeTabId: 'main',
    setActiveTab: jest.fn(),
  })),
}))

// Mock react-hot-toast
jest.mock('react-hot-toast', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}))

// Mock server actions
jest.mock('@/app/server-actions/drag-tree', () => ({
  updateDragTreeLanguage: jest.fn(() => Promise.resolve({ success: true })),
}))

// Mock Stripe
jest.mock('@/lib/stripe/server', () => ({
  createStripePortal: jest.fn(() =>
    Promise.resolve('https://stripe.com/portal')
  ),
}))

// Mock mixpanel
jest.mock('@/app/libs/mixpanel', () => ({
  track: jest.fn(),
}))

// Mock useRoutes hook
jest.mock('@/app/hooks/useRoutes', () => ({
  __esModule: true,
  default: jest.fn(() => []),
}))

// Mock useRouter
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
  })),
}))

const { useSession } = require('next-auth/react')

describe('Permission-Based UI Components', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Language Settings Component', () => {
    const LanguageSettings =
      require('@/app/(conv)/dragTree/[dragTreeId]/components/settings/LanguageSettings').default

    it('should disable language selector for VIEWER tier', () => {
      useSession.mockReturnValue(mockSession(SubscriptionTier.VIEWER))

      render(<LanguageSettings dragTreeId="test-tree" />)

      // Should show explanatory text
      expect(
        screen.getByText('Language preferences cannot be changed')
      ).toBeInTheDocument()

      // Language selector should be disabled
      const languageButton = screen.getByRole('button')
      expect(languageButton).toBeDisabled()
    })

    it('should disable language selector for DUMMY tier', () => {
      useSession.mockReturnValue(mockSession(SubscriptionTier.DUMMY))

      render(<LanguageSettings dragTreeId="test-tree" />)

      expect(
        screen.getByText('Language preferences cannot be changed')
      ).toBeInTheDocument()

      const languageButton = screen.getByRole('button')
      expect(languageButton).toBeDisabled()
    })

    it('should enable language selector for FREE tier', () => {
      useSession.mockReturnValue(mockSession(SubscriptionTier.FREE))

      render(<LanguageSettings dragTreeId="test-tree" />)

      // Should not show restriction text
      expect(
        screen.queryByText('Language preferences cannot be changed')
      ).not.toBeInTheDocument()

      // Language selector should be enabled
      const languageButton = screen.getByRole('button')
      expect(languageButton).not.toBeDisabled()
    })

    it('should enable language selector for PRO tier', () => {
      useSession.mockReturnValue(mockSession(SubscriptionTier.PRO))

      render(<LanguageSettings dragTreeId="test-tree" />)

      expect(
        screen.queryByText('Language preferences cannot be changed')
      ).not.toBeInTheDocument()

      const languageButton = screen.getByRole('button')
      expect(languageButton).not.toBeDisabled()
    })
  })

  describe('SimpleChatInput Component', () => {
    const SimpleChatInput =
      require('@/app/(conv)/dragTree/[dragTreeId]/components/chat/SimpleChatInput').default

    const defaultProps = {
      input: '',
      onInputChange: jest.fn(),
      onSubmit: jest.fn(),
      disabled: false,
      isLoading: false,
    }

    it('should show "Message sending not available" placeholder for restricted permissions', () => {
      render(
        <SimpleChatInput
          {...defaultProps}
          disabled={true}
          placeholder="Message sending not available"
        />
      )

      expect(
        screen.getByPlaceholderText('Message sending not available')
      ).toBeInTheDocument()

      // Input should be disabled
      const textarea = screen.getByRole('textbox')
      expect(textarea).toBeDisabled()

      // Send button should be disabled
      const sendButton = screen.getByRole('button')
      expect(sendButton).toBeDisabled()
    })

    it('should show normal placeholder for allowed permissions', () => {
      render(
        <SimpleChatInput
          {...defaultProps}
          placeholder="Message AI Assistant..."
        />
      )

      expect(
        screen.getByPlaceholderText('Message AI Assistant...')
      ).toBeInTheDocument()

      // Input should be enabled
      const textarea = screen.getByRole('textbox')
      expect(textarea).not.toBeDisabled()
    })
  })

  describe('DesktopSidebar Component', () => {
    const DesktopSidebar =
      require('@/app/components/sidebar/DesktopSidebar').default

    const mockUser = {
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      image: '/test-avatar.jpg',
      subscription_tier: SubscriptionTier.PRO,
    }

    it('should display subscription tier badge', () => {
      render(<DesktopSidebar currentUser={mockUser} />)

      // Should show user name
      expect(screen.getByText('Test User')).toBeInTheDocument()

      // Should show email prefix
      expect(screen.getByText('test')).toBeInTheDocument()

      // Should show subscription tier badge
      expect(screen.getByText('PRO')).toBeInTheDocument()
    })

    it('should handle user without subscription tier', () => {
      const userWithoutTier = { ...mockUser, subscription_tier: null }

      render(<DesktopSidebar currentUser={userWithoutTier} />)

      expect(screen.getByText('Test User')).toBeInTheDocument()
      expect(screen.getByText('test')).toBeInTheDocument()

      // Should not show tier badge
      expect(screen.queryByText('PRO')).not.toBeInTheDocument()
    })
  })

  describe('SettingsModal Component', () => {
    const SettingsModal =
      require('@/app/components/sidebar/SettingsModal').default

    const mockUser = {
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      image: '/test-avatar.jpg',
      subscription_tier: SubscriptionTier.PRO,
    }

    it('should display current plan prominently', () => {
      render(
        <SettingsModal
          isOpen={true}
          onClose={jest.fn()}
          currentUser={mockUser}
        />
      )

      // Should show "Current Plan" label
      expect(screen.getByText('Current Plan')).toBeInTheDocument()

      // Should show subscription tier (raw DB value)
      expect(screen.getByText('PRO')).toBeInTheDocument()

      // Should show premium features indicator for PRO
      expect(screen.getByText('✓ Premium Features')).toBeInTheDocument()
    })

    it('should show FREE tier for users without subscription', () => {
      const freeUser = { ...mockUser, subscription_tier: null }

      render(
        <SettingsModal
          isOpen={true}
          onClose={jest.fn()}
          currentUser={freeUser}
        />
      )

      expect(screen.getByText('Current Plan')).toBeInTheDocument()
      // Raw DB value for free tier
      expect(screen.getByText('FREE')).toBeInTheDocument()

      // Should not show premium features indicator
      expect(screen.queryByText('✓ Premium Features')).not.toBeInTheDocument()

      // Should show upgrade button for FREE users
      expect(screen.getByText('Upgrade to Pro')).toBeInTheDocument()
    })

    it('should handle different subscription tiers', () => {
      // Display shows raw DB tier strings now

      const tiers = [
        SubscriptionTier.VIEWER,
        SubscriptionTier.FREE,
        SubscriptionTier.DUMMY,
        SubscriptionTier.ULTRA,
        SubscriptionTier.BUSINESS,
      ]

      tiers.forEach(tier => {
        const userWithTier = { ...mockUser, subscription_tier: tier }

        const { unmount } = render(
          <SettingsModal
            isOpen={true}
            onClose={jest.fn()}
            currentUser={userWithTier}
          />
        )

        expect(screen.getByText('Current Plan')).toBeInTheDocument()
        expect(screen.getByText(tier)).toBeInTheDocument()

        unmount()
      })
    })
  })

  describe('Permission Integration Tests', () => {
    it('should consistently apply canEditLanguagePreference across components', () => {
      const restrictedTiers = [SubscriptionTier.VIEWER, SubscriptionTier.DUMMY]
      const allowedTiers = [
        SubscriptionTier.FREE,
        SubscriptionTier.PRO,
        SubscriptionTier.GUEST,
        SubscriptionTier.ULTRA,
        SubscriptionTier.BUSINESS,
      ]

      // Test that restricted tiers consistently disable language editing
      restrictedTiers.forEach(tier => {
        useSession.mockReturnValue(mockSession(tier))

        const LanguageSettings =
          require('@/app/(conv)/dragTree/[dragTreeId]/components/settings/LanguageSettings').default
        const { unmount } = render(<LanguageSettings dragTreeId="test" />)

        expect(
          screen.getByText('Language preferences cannot be changed')
        ).toBeInTheDocument()

        unmount()
      })

      // Test that allowed tiers consistently enable language editing
      allowedTiers.forEach(tier => {
        useSession.mockReturnValue(mockSession(tier))

        const LanguageSettings =
          require('@/app/(conv)/dragTree/[dragTreeId]/components/settings/LanguageSettings').default
        const { unmount } = render(<LanguageSettings dragTreeId="test" />)

        expect(
          screen.queryByText('Language preferences cannot be changed')
        ).not.toBeInTheDocument()

        unmount()
      })
    })
  })
})
