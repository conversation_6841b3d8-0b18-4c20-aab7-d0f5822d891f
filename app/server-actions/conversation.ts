'use server'

import prisma from '@/app/libs/prismadb'
import { ConversationLayoutType } from '@/app/types'
import {
  ConversationStatus,
  UserStatus,
  User,
  ConversationType,
  IssueTreeStatus,
  Conversation,
  IssueTree,
} from '@prisma/client'
import getCurrentUser from '@/app/server-actions/getCurrentUser'
import { activeConversationCutoffDate } from '@/app/configs'
import getConversations from '@/app/server-actions/getConversations'
import {
  calculateMaxActiveConversations,
  countActiveConversationsAfterDate,
} from '@/lib/utils'
import { z } from 'zod'
import { screenSchema } from '@/app/api/screening/utils'

type ServerActionError = {
  message: string
  code: number
}

type CreateConversationResult = {
  conversation: ConversationLayoutType | null
  error: ServerActionError | null
}

export const createConversation = async (
  description: string,
  screenObject: Partial<z.infer<typeof screenSchema>> | null
): Promise<CreateConversationResult> => {
  try {
    const currentUser = await getCurrentUser()

    if (!isUserAuthorized(currentUser) || !currentUser) {
      return createErrorResult('User not authorized', 401)
    }

    if (!description.trim()) {
      return createErrorResult('No description provided', 400)
    }

    const conversations = await getConversations()
    if (await hasTooManyActiveConversations(currentUser, conversations)) {
      return createErrorResult('Too many active conversations', 429)
    }

    const newConversation = await createNewConversation(
      currentUser,
      description,
      screenObject
    )
    const newIssueTree = await createNewIssueTree(
      currentUser,
      newConversation,
      description,
      screenObject
    )

    console.log('Conversation created:', newConversation.id)
    console.log('Issue tree created', newIssueTree.id)

    return {
      conversation: sanitizeConversation(newConversation),
      error: null,
    }
  } catch (error) {
    console.error('Error creating conversation:', error)
    return createErrorResult('An unexpected error occurred', 500)
  }
}

function isUserAuthorized(user: User | null): boolean {
  return !!(user?.id && user?.email && user?.status === UserStatus.ACTIVE)
}

function createErrorResult(
  message: string,
  code: number
): CreateConversationResult {
  return {
    conversation: null,
    error: { message, code },
  }
}

async function createNewConversation(
  user: User,
  description: string,
  screenObject: Partial<z.infer<typeof screenSchema>> | null
): Promise<Conversation> {
  const formattedSystemMessage = createSystemMessage(description, screenObject)
  return prisma.conversation.create({
    data: {
      creator_id: user.id,
      conversation_type: ConversationType.ISSUE_TREE,
      prompt_id: 'initial_questions_generation',
      config: {
        prompt: {
          system_message: formattedSystemMessage,
          description: description,
        },
        screen_object: screenObject,
      },
      title: description,
    },
  })
}

async function createNewIssueTree(
  user: User,
  conversation: Conversation,
  description: string,
  screenObject: Partial<z.infer<typeof screenSchema>> | null
): Promise<IssueTree> {
  const formattedSystemMessage = createSystemMessage(description, screenObject)
  return prisma.issueTree.create({
    data: {
      creator_id: user.id,
      conversation_id: conversation.id,
      prompt: formattedSystemMessage,
      status: IssueTreeStatus.INITIALIZED,
      config: {
        directive: 'initial',
        existing_summary: description,
        original_ask: description,
        screen_object: screenObject,
      },
    },
  })
}

function createSystemMessage(
  description: string,
  screenObject: Partial<z.infer<typeof screenSchema>> | null
): string {
  const { intention, entity } = screenObject || {}

  let intentionString = ''
  if (intention && Array.isArray(intention) && intention.length > 0) {
    intentionString = `Intentions:\n${intention.join('\n')}`
  } else if (intention && typeof intention === 'string') {
    intentionString = `Intentions:\n${intention}`
  }

  let entityString = ''
  if (entity && Array.isArray(entity) && entity.length > 0) {
    entityString = `Entities:\n${entity.join('\n')}`
  } else if (entity && typeof entity === 'string') {
    entityString = `Entities:\n${entity}`
  }

  // Embed info from screen object as part of user input, hope the issue tree will be more customized to user's intention and entity
  const finalUserInput = [description, intentionString, entityString]
    .filter(Boolean)
    .join('\n\n')

  return `As a team of elite consultants, we are tasked with diverse client projects and help them to structure the project.

Please generate a list of insightful questions that delve deeply into each aspect of the project, focusing primarily on the underlying rationale ('WHY') and objectives ('WHAT'), but also including other relevant dimensions such as 'HOW', 'WHEN', and 'WHO' where appropriate. We are looking for a multi-layered issue tree that explores each category thoroughly, going several levels deep to cover every important detail.

Use a single level-one heading at the beginning for the project title, followed by sub-headings for categories (##), sub-categories (###), and further sub-divisions as necessary (####, #####). Ensure that each question aligns with its corresponding category and sub-category to maintain the hierarchical structure and depth.

Tailor the questions to the specific domain and context of the user's request, focusing on areas that are most relevant and insightful for that domain. For example, in business-related projects, include topics like market analysis, customer needs, operational challenges, and competitive landscape. In creative or technical projects, focus on elements such as design principles, user experience, implementation strategies, and success metrics.

**Include examples for each question that provide insightful, informative content. These examples should be rich in detail, incorporating best industry practices, relevant statistics, or scenarios that would be valuable to someone new to the field. They should be realistic data points or narratives that can be directly cited or used in a presentation or report. They should NOT be action items or steps for further analysis, and should require NO further elaboration or analysis.**

Examples should be formatted as a continuous sentence following a single "- Examples:" tag.

For example:
# E-commerce Platform Development
## Market Analysis
### Customer Demographics
#### Who are the target customers, and what are their purchasing behaviors?
- Examples: The primary target customers are millennials aged 25-35 who prefer online shopping due to convenience and variety. Studies show that 70% of this demographic shop online at least once a month, with an average spending of $150 per transaction. They value personalized recommendations, with 60% more likely to purchase from platforms that offer tailored product suggestions based on their browsing history.
#### What are the key market trends influencing the e-commerce sector?
- Examples: Mobile commerce is rapidly growing, accounting for 54% of all online sales in the past year. There is a significant shift towards sustainable and ethical products, with a 35% increase in consumers willing to pay more for eco-friendly options. Additionally, the integration of AI chatbots has improved customer service efficiency by 40%, enhancing user satisfaction and loyalty.
### Competitive Landscape
#### How does the platform differentiate itself from competitors?
- Examples: The platform offers a unique subscription model providing members with exclusive discounts and early access to new products, resulting in a 25% higher customer retention rate compared to competitors. It also features an advanced AR tool allowing customers to virtually try products before purchase, which has increased conversion rates by 15%. Furthermore, partnerships with local artisans have expanded product offerings by 30%, attracting niche markets.
## Technology and Infrastructure
### Scalability
#### How will the platform handle increased user traffic during peak times?
- Examples: By implementing cloud-based services like AWS Auto Scaling, the platform can automatically adjust resources, maintaining optimal performance even during traffic spikes of up to 200%. Load balancing strategies distribute user requests efficiently, reducing server response times by 35%. This infrastructure has proven effective during high-demand periods like Black Friday, where transaction volumes tripled without service disruption.
[Don't output this: Continue in the same format, delving deeper into relevant aspects of the project, such as user experience design, security protocols, marketing strategies, etc.]

Ensure that there's only ONE level-one heading (#) in the entire output and that it's at the beginning. Also, ensure that each set of examples is formatted as a continuous sentence after a single "- Examples:" tag.
The user asks: {${finalUserInput}}\n\n
User may give requests not related to system design, do your best to adopt the format exactly to design that, **you have to output the issue tree regardless of the situation**

Now take a breathe and think step by step to structure the problem before output, consider as diverse and deep as possible to cover every important details for this project
`
}

async function hasTooManyActiveConversations(
  user: User | null,
  conversations: ConversationLayoutType[]
): Promise<boolean> {
  const currentActiveConversations = countActiveConversationsAfterDate(
    conversations,
    ConversationStatus.ACTIVE,
    activeConversationCutoffDate
  )
  const maxActiveConversations = calculateMaxActiveConversations(user)
  return currentActiveConversations >= maxActiveConversations
}

function sanitizeConversation(
  conversation: Conversation
): ConversationLayoutType {
  const {
    id,
    creator_id,
    title,
    conversation_type,
    conversation_status,
    is_hidden,
    created_at,
    updated_at,
    config,
  } = conversation

  return {
    id,
    creator_id,
    title,
    conversation_type,
    conversation_status,
    is_hidden,
    created_at,
    updated_at,
    issuetree_updated_at: updated_at,
    config_prompt_description:
      (typeof config === 'object' &&
      config !== null &&
      'prompt' in config &&
      typeof config.prompt === 'object'
        ? (config.prompt as { description?: string }).description
        : null) ?? null,
  }
}
