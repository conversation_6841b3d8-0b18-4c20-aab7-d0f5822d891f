import prisma from '@/app/libs/prismadb'

const getMessages = async (conversationId: string) => {
  try {
    const messages = await prisma.message.findMany({
      where: {
        conversation_id: conversationId,
        // TODO: We don't have delete functionality yet, but we may
        // we want to just hide the message instead of deleting it
        is_hidden: false,
        // We don't want to leak the system prompt to the user
        // Hence for each conversation, only return messages that are not system role
        creator_id: {
          not: 'system',
        },
      },
      orderBy: {
        created_at: 'asc',
      },
    })

    return messages
  } catch {
    return []
  }
}

export default getMessages
