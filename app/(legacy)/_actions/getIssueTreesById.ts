import prisma from '@/app/libs/prismadb'
import { IssueTreeStatus } from '@prisma/client'

const getIssueTreesById = async (conversationId: string, currentUser: any) => {
  try {
    if (!currentUser?.email) {
      return null
    }
    // Get latest issue tree, one conversation may have multiple issue trees
    const issueTrees = await prisma.issueTree.findMany({
      where: {
        conversation_id: conversationId,
        status: IssueTreeStatus.COMPLETED,
      },
      select: {
        id: true,
        conversation_id: true,
        nodes: true,
      },
    })

    return issueTrees
  } catch (error: any) {
    console.log(error, 'SERVER_ERROR')
    return null
  }
}

export default getIssueTreesById
