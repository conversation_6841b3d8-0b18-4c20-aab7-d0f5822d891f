import prisma from '@/app/libs/prismadb'
import getCurrentUser from './getCurrentUser'

const getPromptById = async (coworker: string) => {
  try {
    const currentUser = await getCurrentUser()

    if (!currentUser?.email) {
      return null
    }

    const prompt = await prisma.prompt.findUnique({
      where: {
        id: coworker,
      },
    })

    return prompt
  } catch (error: any) {
    console.log(error, 'SERVER_ERROR')
    return null
  }
}

export default getPromptById
