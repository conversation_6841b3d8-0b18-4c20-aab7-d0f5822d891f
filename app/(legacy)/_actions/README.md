# Legacy Actions Directory

⚠️ **WARNING: This directory contains LEGACY code that should NOT be used in new implementations.**

## Do NOT Use These Actions

The functions in this directory are deprecated and should be avoided:

- `getCurrentUser()` - Use `getServerSession(authOptions)` from NextAuth instead
- `getConversationById()` - Legacy conversation management
- `getConversations()` - Legacy conversation management
- `getSession()` - Use `getServerSession(authOptions)` directly

## Modern Alternatives

For new code, use these approaches:

### Authentication

```typescript
// ✅ Modern approach
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'

export async function myServerAction() {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return { success: false, error: 'Unauthorized' }
  }
  // Use session.user.id for user operations
}

// ❌ Legacy approach (DON'T USE)
import getCurrentUser from '@/app/actions/getCurrentUser'
const user = await getCurrentUser()
```

### Data Operations

- Use server actions in `/app/server-actions/` directory
- Use direct Prisma queries with proper auth validation
- Follow the patterns in `/app/server-actions/drag-tree/index.ts`

## Migration Notes

This directory exists for backward compatibility only. All new features should use:

1. Server actions with `'use server'` directive
2. Direct `getServerSession(authOptions)` for auth
3. Modern Next.js 14+ patterns

**Do not add new functions to this directory.**
