// Import the Prisma client instance
import prisma from '@/app/libs/prismadb'

// Import the getSession function from the same directory
import getSession from './getSession'

// Define an asynchronous function called getUsers
const getUsers = async () => {
  // Call the getSession function to get the user's session
  const session = await getSession()

  // If the session does not exist or the user's email is not found in the session, return an empty array
  if (!session?.user?.email) {
    return []
  }

  try {
    // Use the Prisma client instance to find all users except the current user, ordered by creation date in descending order
    const users = await prisma.user.findMany({
      orderBy: {
        created_at: 'desc',
      },
      where: {
        // Temp set this as retrieving the user it self
        email: {
          equals: session.user.email as string,
        },
      },
    })

    // Return the array of users
    return users
  } catch {
    // If an error occurs, return an empty array
    return []
  }
}

// Export the getUsers function as the default export of this module
export default getUsers
