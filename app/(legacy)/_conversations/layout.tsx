// LEGACY CONVERSATION LAYOUT - COMMENTED OUT FOR V2 MIGRATION
// This layout is part of the old conversation-based architecture (v1)
// The dragTree system now provides the universal layout

interface ConversationsLayoutProps {
  children: React.ReactNode
}

const ConversationsLayout: React.FC<ConversationsLayoutProps> = async ({
  children,
}) => {
  // Legacy layout disabled - children will be handled by redirect in page.tsx
  return <>{children}</>
}

export default ConversationsLayout
