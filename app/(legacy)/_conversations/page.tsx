// LEGACY CONVERSATION PAGE - COMMENTED OUT FOR V2 MIGRATION
// This page is part of the old conversation-based architecture (v1)
// Users should now use the dragTree system at /dragTree/[id] instead

// Redirect users to the modern dragTree system
import { redirect } from 'next/navigation'

const Home = async () => {
  // Redirect to dragTree system - users can create new trees there
  redirect('/dragTree')
}

export default Home
