import { ConversationStatus, Notebook } from '@prisma/client'
import NotebookEditor from '@/app/components/editor/NotebookEditor'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import prisma from '@/app/libs/prismadb'
type NotebookIdParams = {
  conversationId: string
  notebookId: string
}

export const dynamic = 'force-dynamic'
export const revalidate = 0

const NotebookId: React.FC<{ params: Promise<NotebookIdParams> }> = async ({
  params,
}) => {
  const { conversationId, notebookId } = await params
  const session = await getServerSession(authOptions)
  const conversation = await prisma.conversation.findUnique({
    where: { id: conversationId },
    select: { creator_id: true, conversation_status: true },
  })
  // Check user permissions
  const hasPermission =
    session?.user?.id === conversation?.creator_id ||
    session?.user?.email === '<EMAIL>' ||
    conversation?.conversation_status === ConversationStatus.EXAMPLE

  if (!hasPermission) {
    return (
      <h1 className="lg:pl-20 text-2xl">
        You have no permission to access this conversation
      </h1>
    )
  }

  const notebook = await prisma.notebook.findUnique({
    where: { id: notebookId },
  })

  if (!notebook) {
    return (
      <h1 className="lg:pl-20 text-2xl">
        Ops, some issues, Notebook not found
      </h1>
    )
  }

  return (
    <div className="lg:pl-20 lg:pr-10 h-screen">
      <NotebookEditor notebook={notebook as Notebook} />
    </div>
  )
}

export default NotebookId
