import React, { memo, useCallback } from 'react'
import { <PERSON><PERSON>, Position } from 'reactflow'
import useSubtreeStore from '@/app/stores/subtree_store'
import mixpanel from '@/app/libs/mixpanel'
import useCheckDisabledConversation from '@/app/hooks/useCheckDisabledConversation'

type CustomInternalNodeProps = {
  id: string
  data: {
    label: string
  }
}

const CustomInternalNode: React.FC<CustomInternalNodeProps> = memo(
  ({ id: selectedNodeId, data }) => {
    const { subtrees, onOpen } = useSubtreeStore()
    const shouldDisable = useCheckDisabledConversation()

    const subtreeExists = !!subtrees[selectedNodeId]

    const handleOpenSubtree = useCallback(() => {
      mixpanel.track('open_subtree', {
        location: 'issue_tree',
        selected_node_id: selectedNodeId,
        selected_node_label: data.label,
      })
      onOpen(selectedNodeId)
    }, [selectedNodeId, data.label, onOpen])

    return (
      <article className="relative flex justify-center items-center p-2 bg-yellow-200 rounded-lg shadow-md transition-all duration-300 hover:shadow-lg hover:bg-yellow-300">
        <h3 className="text-center font-bold text-base text-zinc-800 pt-4">
          {data.label}
        </h3>
        {!shouldDisable && (
          <button
            className={`absolute right-0 top-0 pb-2 pl-2 text-sm transition-all duration-300 rounded-tr-lg rounded-bl-lg
            ${
              subtreeExists
                ? 'text-white bg-blue-500 bg-opacity-50 hover:bg-opacity-70'
                : 'text-zinc-600 bg-gray-300 bg-opacity-50 hover:text-white hover:bg-green-500 hover:bg-opacity-70'
            }
            hover:font-bold hover:shadow-md`}
            onClick={handleOpenSubtree}
          >
            {subtreeExists ? 'Continue' : 'Expand'}
          </button>
        )}
        <Handle
          type="target"
          position={Position.Left}
          className="w-6 h-6 bg-yellow-500"
        />
        <Handle
          type="source"
          position={Position.Right}
          className="w-6 h-6 bg-yellow-500"
        />
      </article>
    )
  }
)

CustomInternalNode.displayName = 'CustomInternalNode'

export default CustomInternalNode
