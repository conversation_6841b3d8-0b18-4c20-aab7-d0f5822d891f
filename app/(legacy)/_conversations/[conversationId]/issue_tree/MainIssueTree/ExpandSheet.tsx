import {
  Sheet,
  SheetContent,
  Sheet<PERSON><PERSON><PERSON>,
  Sheet<PERSON>eader,
  Sheet<PERSON>itle,
} from '@/components/ui/sheet'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import useIssueTreeStore from '@/app/stores/issuetree_store'
import { useConversationStore } from '@/app/stores/conversation_store'
import { toast } from 'react-hot-toast'
import NotebookDialog from '../NotebookDialog'

interface ExpandSheetProps {
  showSheet: boolean
  closeSheet: () => void
  conversationId: string
}

export default function ExpandSheet({
  showSheet,
  closeSheet,
  conversationId,
}: ExpandSheetProps) {
  const { conversationList } = useConversationStore()
  const {
    originalAskText,
    treeMarkdownText,
    issueTreeId,
    unresolvedNodesCount,
    skippedNodesCount,
    leafNodeCount,
  } = useIssueTreeStore()

  const conversation = conversationList.find(conv => conv.id === conversationId)
  const originalAsk =
    originalAskText ||
    'THIS SHOULD SHOW THE ORIGINAL ASK, if you see this and need to know the original ask, refreshing the page usually works'

  // Helper function to safely convert values to string
  const safeToString = (value: unknown): string =>
    value != null && typeof value['toString'] === 'function'
      ? value.toString()
      : 'N/A'

  // Helper function to copy text to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => toast.success('Copied to clipboard!'))
      .catch(err => toast.error(`Could not copy text: ${err}`))
  }

  return (
    <Sheet key="right" open={showSheet} onOpenChange={closeSheet}>
      <SheetContent>
        <SheetTitle>Issue Tree Details</SheetTitle>
        <Tabs defaultValue="meta-data">
          <TabsList>
            <TabsTrigger value="meta-data">Metadata</TabsTrigger>
          </TabsList>
          <TabsContent
            value="meta-data"
            className="flex flex-col justify-between py-2"
          >
            <NotebookDialog />
            {/* Original Ask Section */}
            <MetadataSection
              title="Original Ask"
              text={originalAsk}
              onCopy={() => copyToClipboard(originalAsk)}
            />

            {/* Markdown Format Section */}
            {treeMarkdownText && (
              <MetadataSection
                title="Markdown format"
                text={treeMarkdownText}
                onCopy={() => copyToClipboard(treeMarkdownText)}
              />
            )}

            {/* Metadata Descriptions */}
            <SheetDescription className="p-2">
              {'Issue tree id: ' + safeToString(issueTreeId)}
            </SheetDescription>
            <SheetDescription className="p-2">
              {'Created at: ' +
                (conversation?.created_at?.toLocaleString() ?? 'N/A')}
            </SheetDescription>
            <SheetDescription className="p-2">
              {'Needs Review Questions: ' + safeToString(unresolvedNodesCount)}
            </SheetDescription>
            <SheetDescription className="p-2">
              {'Irrelevant Questions: ' + safeToString(skippedNodesCount)}
            </SheetDescription>
            <SheetDescription className="p-2">
              {'Total Questions: ' + safeToString(leafNodeCount)}
            </SheetDescription>
          </TabsContent>
        </Tabs>
      </SheetContent>
    </Sheet>
  )
}

// Reusable component for metadata sections
function MetadataSection({
  title,
  text,
  onCopy,
}: {
  title: string
  text: string
  onCopy: () => void
}) {
  return (
    <>
      <SheetHeader className="flex flex-row justify-between items-center py-2">
        <h2 className="p-2 text-zinc-700">{title}</h2>
        <Button
          className="bg-purple-100 hover:bg-purple-200 text-gray-500"
          size="sm"
          onClick={onCopy}
        >
          Copy
        </Button>
      </SheetHeader>
      <Textarea
        rows={title === 'Original Ask' ? 5 : 10}
        placeholder={text}
        disabled
      />
    </>
  )
}
