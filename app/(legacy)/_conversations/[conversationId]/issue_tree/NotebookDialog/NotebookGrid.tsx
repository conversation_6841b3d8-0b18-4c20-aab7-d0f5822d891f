import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { PlusCircle, Coffee } from 'lucide-react'
import { useRouter, useParams } from 'next/navigation'
import useNotebookStore, { FetchedNotebook } from '@/app/stores/notebook_store'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import Link from 'next/link'
// import { toast } from "react-hot-toast";
import mixpanel from '@/app/libs/mixpanel'

type NotebookGridProps = {
  onNewNotebookClick: () => void
  notebookCountLimit: number
}

export function NotebookGrid({
  onNewNotebookClick,
  notebookCountLimit,
}: NotebookGridProps) {
  const router = useRouter()
  const params = useParams()
  const notebookStore = useNotebookStore()
  const conversationId = params?.conversationId as string
  const canCreateNewNotebook =
    notebookStore.notebooks.length < notebookCountLimit

  const handleNotebookClick = (notebook: FetchedNotebook) => {
    mixpanel.track('click_notebook', {
      location: 'notebook_grid',
    })
    notebookStore.setShowDialog(false)
    router.push(`/conversations/${conversationId}/notebook/${notebook.id}`)
  }

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 auto-rows-fr">
        <Card
          className={`flex flex-col justify-center items-center ${
            canCreateNewNotebook
              ? 'cursor-pointer hover:bg-gray-200 bg-gray-50'
              : 'bg-gray-100 hover:bg-gray-200 opacity-50 cursor-pointer'
          }`}
          onClick={onNewNotebookClick}
        >
          <CardHeader>
            <CardTitle
              className={`text-lg font-medium text-center ${
                canCreateNewNotebook ? '' : 'text-gray-500'
              }`}
            >
              New Notebook
              <br />
              <span className="text-sm text-gray-500">
                ({notebookCountLimit - notebookStore.notebooks.length}{' '}
                avaliable)
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <PlusCircle className="h-12 w-12 text-gray-400" />
          </CardContent>
        </Card>
        {notebookStore.notebooks.map(notebook => (
          <NotebookCard
            key={notebook.id}
            notebook={notebook}
            onClick={() => handleNotebookClick(notebook)}
            conversationId={conversationId}
          />
        ))}
      </div>
      <h2 className="text-2xl font-bold my-4">
        Support us if you think this is helpful!
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 auto-rows-fr">
        {/* {exampleNotebooks.map((notebook) => (
          <NotebookCard
            key={notebook.id}
            notebook={notebook}
            onClick={() => handleNotebookClick(notebook)}
            conversationId={conversationId}
          />
        ))} */}
        <a
          href="https://www.buymeacoffee.com/clarifyai"
          target="_blank"
          rel="noreferrer"
          onClick={() => {
            mixpanel.track('buy_me_a_coffee_click', {
              location: 'notebook_grid',
            })
          }}
          className="block h-full"
        >
          <Card className="cursor-pointer hover:bg-gray-100 flex flex-col h-full">
            <CardHeader className="flex-grow">
              <CardTitle className="text-lg font-medium line-clamp-2">
                Even a small amount helps!
              </CardTitle>
            </CardHeader>
            <CardContent className="mt-auto">
              <div className="flex items-center justify-center">
                <Coffee className="h-12 w-12 text-yellow-500 mr-2" />
                <span className="text-sm text-gray-600">
                  This helps to cover API and server costs!
                </span>
              </div>
            </CardContent>
          </Card>
        </a>
      </div>
    </>
  )
}

type NotebookCardProps = {
  notebook: FetchedNotebook
  onClick: () => void
  conversationId: string
}

function NotebookCard({
  notebook,
  onClick,
  conversationId,
}: NotebookCardProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link
            href={`/conversations/${conversationId}/notebook/${notebook.id}`}
            passHref
            className="h-full"
          >
            <Card
              className="cursor-pointer hover:bg-gray-100 flex flex-col h-full"
              onClick={e => {
                e.preventDefault()
                onClick()
              }}
            >
              <CardHeader className="flex-grow">
                <CardTitle className="text-lg font-medium line-clamp-2">
                  {notebook.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="mt-auto">
                <p className="text-sm text-gray-500">
                  {new Date(notebook.updated_at).toLocaleString('en-US', {
                    weekday: 'short',
                    year: 'numeric',
                    month: 'short',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    timeZone: 'PST',
                    hour12: false,
                  })}
                </p>
              </CardContent>
            </Card>
          </Link>
        </TooltipTrigger>
        <TooltipContent>
          <div>
            <p className="font-bold">{notebook.title}</p>
            <p className="text-sm text-gray-500">
              {new Date(notebook.updated_at).toLocaleString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                timeZone: 'PST',
                hour12: false,
              })}
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
