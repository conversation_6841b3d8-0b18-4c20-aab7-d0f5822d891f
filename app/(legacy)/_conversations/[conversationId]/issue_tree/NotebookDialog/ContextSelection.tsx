import React, { useState, useEffect } from 'react'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import useIssueTreeStore from '@/app/stores/issuetree_store'

export type ContextSelectionState = {
  useAllContexts: boolean
}

type ContextSelectionProps = {
  onSelect: (selectedContexts: ContextSelectionState) => void
}

export function ContextSelection({ onSelect }: ContextSelectionProps) {
  const [selectedContexts, setSelectedContexts] =
    useState<ContextSelectionState>({
      useAllContexts: false,
    })

  const issueTreeStore = useIssueTreeStore()
  const resolvedNodeCount =
    issueTreeStore.leafNodeCount - issueTreeStore.unresolvedNodesCount
  const leafNodeCount = issueTreeStore.leafNodeCount
  useEffect(() => {
    onSelect(selectedContexts)
  }, [selectedContexts, onSelect])

  const handleSwitchChange = (contextId: keyof ContextSelectionState) => {
    setSelectedContexts(prev => ({
      ...prev,
      [contextId]: !prev[contextId],
    }))
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Switch
          id="use-all-contexts"
          checked={selectedContexts.useAllContexts}
          onCheckedChange={() => handleSwitchChange('useAllContexts')}
        />
        <Label htmlFor="use-all-contexts">
          {selectedContexts.useAllContexts
            ? `[${leafNodeCount}/${leafNodeCount}] Using ALL nodes, including those need your reviews`
            : `[${resolvedNodeCount}/${leafNodeCount}] Using resolved nodes only`}
        </Label>
      </div>
    </div>
  )
}
