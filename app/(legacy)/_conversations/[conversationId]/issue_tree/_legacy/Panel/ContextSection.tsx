import { useEffect, useState, useMemo } from 'react'
import useIssueTreeStore from '@/app/stores/issuetree_store'
import { usePanelStore } from '@/app/stores/panel_store'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import CopyButton from './CopyButton'
import PromptPanelFeedback from './PromptPanelFeedback'
import mixpanel from '@/app/libs/mixpanel'
import { Node } from '@/app/types'

const ContextSection: React.FC = () => {
  const issueTreeStore = useIssueTreeStore()
  const panelStore = usePanelStore()

  const [isValidQsOnly, setIsValidQsOnly] = useState<boolean>(false)
  const [showFeedback, setShowFeedback] = useState<boolean>(false)

  // Generate a string of valid questions and their answers
  const getAnsweredQuestionsString = (nodes: Node[]): string => {
    return nodes
      .filter(
        node =>
          node.type === 'customLeafNode' &&
          node.data.resolved &&
          !node.data.skipped
      )
      .map(node => `Question: ${node.data.label}\nAnswer: ${node.data.example}`)
      .join('\n\n')
  }

  const displayedWorkingContext = useMemo(
    () =>
      isValidQsOnly
        ? getAnsweredQuestionsString(issueTreeStore.nodes)
        : issueTreeStore.treeMarkdownText,
    [isValidQsOnly, issueTreeStore.nodes, issueTreeStore.treeMarkdownText]
  )

  useEffect(() => {
    if (panelStore.workingContext !== displayedWorkingContext) {
      panelStore.setWorkingContext(displayedWorkingContext)
    }
  }, [displayedWorkingContext, panelStore])

  const handleOpenFeedback = () => {
    mixpanel.track('open_prompt_panel_feedback')
    setShowFeedback(true)
  }

  const handleCloseFeedback = () => setShowFeedback(false)

  return (
    <section className="space-y-4 sticky top-0 self-start">
      <div className="space-y-2">
        <h1 className="text-2xl font-bold tracking-tighter">Context Preview</h1>

        {issueTreeStore.unresolvedNodesCount !== 0 && (
          <p className="text-red-500 text-sm">
            <strong>Garbage In, Garbage Out Warning:</strong> There are{' '}
            {issueTreeStore.unresolvedNodesCount} unreviewed questions. Be aware
            of the INACCURATE or IRRELEVANT context.
          </p>
        )}

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Switch
              id="valid-only"
              checked={isValidQsOnly}
              onClick={() => setIsValidQsOnly(!isValidQsOnly)}
            />
            <Label htmlFor="valid-only" className="ml-2">
              {isValidQsOnly
                ? `Using resolved ${
                    issueTreeStore.leafNodeCount -
                    issueTreeStore.unresolvedNodesCount -
                    issueTreeStore.skippedNodesCount
                  } Qs`
                : 'Using all questions'}
            </Label>
          </div>
        </div>

        <ContextPreview
          title="Working Context"
          text={displayedWorkingContext}
        />
        <ContextPreview
          title="Original Ask"
          text={issueTreeStore.originalAskText}
          rows={4}
        />

        <div className="flex flex-row items-center justify-center">
          <Button
            className="bg-green-200 hover:bg-green-300 text-zinc-500 font-bold"
            onClick={handleOpenFeedback}
          >
            We love feedback!
          </Button>
        </div>

        {showFeedback && (
          <PromptPanelFeedback handleCloseFeedback={handleCloseFeedback} />
        )}
      </div>
    </section>
  )
}

type ContextPreviewProps = {
  title: string
  text: string
  rows?: number
}

const ContextPreview: React.FC<ContextPreviewProps> = ({
  title,
  text,
  rows = 8,
}) => (
  <>
    <div className="flex flex-row justify-between items-center">
      <h2 className="text-lg font-bold tracking-tighter">{title}</h2>
      <CopyButton
        promptTitle={title}
        buttonText="Copy"
        url=""
        copiedText={text}
      />
    </div>
    <Textarea
      value={text}
      rows={rows}
      disabled
      className="mt-2 w-full text-zinc-300 hover:text-zinc-800"
    />
  </>
)

export default ContextSection
