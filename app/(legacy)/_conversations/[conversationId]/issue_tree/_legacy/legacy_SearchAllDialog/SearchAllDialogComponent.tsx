// import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
// import React from "react";
// import useIssueTreeStore from "@/app/stores/issuetree_store";
// import { IssueTreeStateType } from "@/app/stores/issuetree_store";
// import QuestionsList from "./QuestionsList";
// import NestedCheckbox from "./testNestedList";
// import { Node, Edge } from "@/app/types";

// export type ExtendedNode = Node & {
//   categories: string[];
// };

// const SearchAllDialog: React.FC = () => {
//   const issueTreeStore = useIssueTreeStore();

//   const validQuestionsWithCategories =
//     getValidQuestionsWithCategories(issueTreeStore);

//   console.log(validQuestionsWithCategories);

//   return (
//     <Dialog
//       open={issueTreeStore.selectedNode?.isOpen}
//       onOpenChange={issueTreeStore.onClose}
//     >
//       <DialogContent className="lg:max-w-[1000px] sm:max-w-[625px] p-6 overflow-hidden">
//         <div className="flex space-x-4">
//           <div className="flex-1">
//             <DialogTitle className="rounded p-2">
//               Let me search all for you
//               <br />
//               <br />
//             </DialogTitle>
//             <NestedCheckbox />
//             {/* <QuestionsList questions={validQuestionsWithCategories} /> */}
//           </div>
//         </div>
//       </DialogContent>
//     </Dialog>
//   );
// };

// export default SearchAllDialog;

// // Function to find the path from root to each node, excluding CustomLeafNode label
// const findPath = (nodeId: string, nodes: Node[], edges: Edge[]): string[] => {
//   const path = [];
//   let currentNodeId = nodeId;

//   while (currentNodeId !== "L1:1") {
//     const node = nodes.find((n) => n.id === currentNodeId);
//     if (node && node.type !== "customLeafNode") {
//       path.unshift(node.data?.label || currentNodeId);
//     }
//     const parentEdge = edges.find((edge) => edge.target === currentNodeId);
//     if (parentEdge) {
//       currentNodeId = parentEdge.source;
//     } else {
//       break;
//     }
//   }

//   // Add the root node at the beginning of the path
//   const rootNode = nodes.find((n) => n.id === "L1:1");
//   if (rootNode) {
//     path.unshift(rootNode.data?.label || "L1:1");
//   }

//   return path;
// };

// // Helper function to get valid questions with categories
// const getValidQuestionsWithCategories = (
//   issueTreeStore: IssueTreeStateType
// ): ExtendedNode[] => {
//   const nodes = issueTreeStore.nodes;
//   const edges = issueTreeStore.edges;

//   const validQuestions = nodes.filter(
//     (node) =>
//       node.type === "customLeafNode" &&
//       !issueTreeStore.searches.some(
//         (search) => search.selected_node_id === node.id
//       )
//   );

//   return validQuestions.map((question) => {
//     const categories = findPath(question.id, nodes, edges);
//     return {
//       ...question,
//       categories,
//     };
//   });
// };
