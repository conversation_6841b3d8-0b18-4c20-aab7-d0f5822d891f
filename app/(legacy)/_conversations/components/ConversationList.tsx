'use client'

import { useEffect, useState } from 'react'
import { MdNoteAdd, MdClose } from 'react-icons/md'
import clsx from 'clsx'
import { ArrowRightFromLine } from 'lucide-react'
import { FiMenu } from 'react-icons/fi'
import useConversation from '@/app/hooks/useConversation'
import ConversationBox from './ConversationBox'
import { toast } from 'react-hot-toast'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { useConversationStore } from '@/app/stores/conversation_store'
import { ConversationStatus, User } from '@prisma/client'
import { activeConversationCutoffDate } from '@/app/configs'
import { countActiveConversationsAfterDate } from '@/lib/utils'
import mixpanel from '@/app/libs/mixpanel'
import { calculateMaxActiveConversations } from '@/lib/utils'
import { ConversationLayoutType } from '@/app/types'

type ConversationListProps = {
  initialItems: ConversationLayoutType[]
  currentUser?: User | null
}

const ConversationList: React.FC<ConversationListProps> = ({
  initialItems,
  currentUser,
}) => {
  const { conversationId, isOpen } = useConversation()

  const session = useSession()
  const router = useRouter()
  const [isSidebarVisible, setSidebarVisible] = useState(false)

  // Add a new state variable for mobile sidebar visibility
  const [isMobileSidebarVisible, setIsMobileSidebarVisible] = useState(false)

  // Function to toggle mobile sidebar visibility
  const toggleMobileSidebar = () => {
    setIsMobileSidebarVisible(!isMobileSidebarVisible)
  }

  const closeMobileSidebar = () => {
    setIsMobileSidebarVisible(false)
  }

  const windowMaxActiveConversations = calculateMaxActiveConversations(
    currentUser || null
  )

  // Try to use the state inside conversationStore to maintain the conversation list

  // This approach is necessary because when we create or update a conversation,
  // the database gets updated, but the frontend components (like the conversation list)
  // don't immediately know about it.

  // To see the new data, we usually have to refresh the page, which can be inconvenient.
  // However, since our current usage doesn't involve real-time multiple users, we don't
  // need complex web socket solutions. By using the conversationStore to manage the states,
  // we can achieve this with minimal effort.

  // Above is what I wrote when I first started, with more experience, now maybe we can pass
  // the reload function here, so it just fetch the conversation list from the server
  // But not worth to redo, just leave as note
  const conversationStore = useConversationStore()
  useEffect(() => {
    conversationStore.setConversationList(initialItems)
  }, [initialItems])

  // TODO: Workaround for responsive view when the sidebar is closed
  // Ideally /new should be /conversations/new
  const onClick = () => {
    if (session?.status === 'authenticated') {
      router.push('/conversations')
    } else {
      toast.error('Please login to create a new conversation')
    }
  }

  const handleConversationClick = (_id: string) => {
    // Hide the mobile sidebar
    setIsMobileSidebarVisible(false)
  }

  const trackPHClick = () => {
    mixpanel.track('product_hunt_click', { location: 'conversation_list' })
  }

  const currentActiveSessionsCount = countActiveConversationsAfterDate(
    conversationStore.conversationList,
    ConversationStatus.ACTIVE,
    activeConversationCutoffDate
  )

  const sortedConversations = conversationStore.conversationList
    .filter(
      item =>
        item.issuetree_updated_at instanceof Date ||
        item.updated_at instanceof Date
    )
    .sort((a, b) => {
      const aDate =
        a.issuetree_updated_at instanceof Date
          ? a.issuetree_updated_at
          : a.updated_at
      const bDate =
        b.issuetree_updated_at instanceof Date
          ? b.issuetree_updated_at
          : b.updated_at
      return bDate.getTime() - aDate.getTime()
    })

  return (
    <>
      {/* Web View */}
      {isSidebarVisible && (
        <aside
          onMouseLeave={() => setSidebarVisible(false)}
          className={clsx(
            'fixed inset-y-0 pb-20 lg:pb-0 lg:left-20 lg:w-80 lg:block overflow-y-auto border-r bg-white border-gray-100 transition-all duration-300 ease-in-out z-10',
            isOpen ? 'hidden' : 'block w-full left-0'
          )}
        >
          <div className="px-5 bg-white">
            <div className="flex justify-between mb-4 pt-4">
              <div className="text-lg md:text-base font-bold text-neutral-800">
                Active Sessions: {currentActiveSessionsCount} (Max{' '}
                {windowMaxActiveConversations})
              </div>
              <div
                onClick={() => onClick()}
                className="md:hidden rounded-full p-2 bg-gray-100 text-gray-600 cursor-pointer hover:opacity-75 transition"
              >
                <MdNoteAdd size={20} />
              </div>
            </div>
            <a
              href="https://www.producthunt.com/posts/clarify-ai?utm_source=badge-featured&utm_medium=badge&utm_souce=badge-clarify&#0045;ai"
              target="_blank"
              onClick={trackPHClick}
            >
              <img
                src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=421464&theme=light"
                alt="Clarify&#0032;AI - Transform&#0032;vague&#0032;ideas&#0032;into&#0032;structured&#0032;insights | Product Hunt"
                style={{ width: '250px', height: '54px' }}
                width="250"
                height="54"
              />
            </a>
            {/* Use data in conversationStore to display the state */}
            {sortedConversations.map(item => (
              <ConversationBox
                key={item.id}
                data={item}
                selected={conversationId === item.id}
              />
            ))}
          </div>
        </aside>
      )}

      {/* Gray vertical bar for Web - Hide on medium screens */}
      <div className="hidden lg:block">
        <div
          onMouseEnter={() => setSidebarVisible(true)}
          className="transition-all duration-300 ease-in-out fixed inset-y-0 left-20 bg-gray-200 p-2 cursor-pointer"
        >
          <div className="flex items-center justify-center h-full">
            <ArrowRightFromLine />
          </div>
        </div>
      </div>

      {/* Hamburger menu for medium and small screens */}
      <div className="lg:hidden fixed bottom-0 right-0 z-50 p-4">
        <button onClick={toggleMobileSidebar}>
          <FiMenu size={24} />
        </button>
      </div>

      {/* Mobile Sidebar */}
      {isMobileSidebarVisible && (
        <div className="fixed inset-0 bg-white z-40 overflow-y-auto">
          <div className="px-5 bg-white">
            <div className="flex justify-between mb-4 pt-4">
              <div className="text-xl font-bold text-neutral-800">
                Active Sessions: {currentActiveSessionsCount} (Max{' '}
                {windowMaxActiveConversations})
              </div>
              <div className="flex space-x-2">
                <div
                  onClick={() => {
                    setIsMobileSidebarVisible(false)
                    onClick()
                  }}
                  className="rounded-full p-2 bg-gray-100 text-gray-600 cursor-pointer hover:opacity-75 transition"
                >
                  <MdNoteAdd size={20} />
                </div>
                <div
                  onClick={closeMobileSidebar}
                  className="rounded-full p-2 bg-gray-100 text-gray-600 cursor-pointer hover:opacity-75 transition"
                >
                  <MdClose size={20} />
                </div>
              </div>
            </div>
            {/* Use data in conversationStore to display the state */}
            {sortedConversations.map(item => (
              <div
                onClick={() => handleConversationClick(item.id)}
                key={item.id}
              >
                <ConversationBox
                  data={item}
                  selected={conversationId === item.id}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  )
}

export default ConversationList
