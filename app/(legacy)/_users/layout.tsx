import getUsers from '@/app/server-actions/getUsers'
import Sidebar from '@/app/components/sidebar/Sidebar'
import UserList from './components/UserList'
import getCurrentUser from '@/app/server-actions/getCurrentUser'

export default async function UsersLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const users = await getUsers()
  const currentUser = await getCurrentUser()

  if (!currentUser) {
    return <div>Cannot find Current User</div>
  }

  return (
    <Sidebar>
      <div className="h-full">
        <UserList items={users} />
        {children}
      </div>
    </Sidebar>
  )
}
