import { User } from '@prisma/client'

import Avatar from '@/app/components/Avatar'
import { toast } from 'react-hot-toast'
// import LoadingModal from "@/app/components/modals/LoadingModal";

interface UserBoxProps {
  data: User
}

const UserBox: React.FC<UserBoxProps> = ({ data }) => {
  // Commented out unused variables for future use
  // const router = useRouter();
  // const [isLoading, setIsLoading] = useState(false);

  const handleClick = () => {
    toast.success('Clicked!')
  }
  // const handleClick = useCallback(() => {
  //   setIsLoading(true);
  //   console.log(data, "UserBox.tsx");
  //   axios
  //     .post("/api/conversations", { data })
  //     .then((data) => {
  //       router.push(`/conversations/${data.data.id}`);
  //     })
  //     .finally(() => setIsLoading(false));
  // }, [data, router]);

  return (
    <>
      {/* {isLoading && <LoadingModal />} */}
      <div
        onClick={handleClick}
        className="
          w-full
          relative
          flex
          items-center
          space-x-3
          bg-white
          p-3
          hover:bg-neutral-100
          rounded-lg
          transition
          cursor-pointer
        "
      >
        <Avatar user={data} />
        <div className="min-w-0 flex-1">
          <div className="focus:outline-none">
            <span className="absolute inset-0" aria-hidden="true" />
            <div className="flex justify-between items-center mb-1">
              <p className="text-sm font-medium text-gray-900">{data.name}</p>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default UserBox
