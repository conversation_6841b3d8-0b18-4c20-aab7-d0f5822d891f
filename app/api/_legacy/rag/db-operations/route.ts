import { NextResponse } from 'next/server'
import prisma from '@/app/libs/prismadb'
import { OpenAIUsageType } from '@prisma/client'
import { ragAssistantRequestType } from '@/app/types/api'
import { RAGDBOperations } from '@/app/api/rag/utils'
import { logOpenAIUsage_serverAction } from '@/app/server-actions/log_openai_usage'

export async function POST(req: Request) {
  const { operation, data } = await req.json()

  switch (operation) {
    case RAGDBOperations.LOG_SEARCH:
      return handleLogSearch(data)
    case RAGDBOperations.LOG_AND_SAVE_RESPONSE:
      return handleLogAndSaveResponse(data)
    default:
      return NextResponse.json({ error: 'Invalid operation' }, { status: 400 })
  }
}

async function handleLogSearch(data: {
  searchQuery: string
  searchResults: any
  requestData: ragAssistantRequestType
  model_name: string
  systemPrompt: string
}) {
  const { searchQuery, searchResults, requestData, model_name, systemPrompt } =
    data

  if (!requestData.userId) {
    return NextResponse.json(
      { message: 'Unauthorized. User ID is required.' },
      { status: 401 }
    )
  }

  await Promise.all([
    logOpenAIUsage_serverAction({
      open_ai_usage_type: OpenAIUsageType.RAG_QUERY_REPHRASER,
      model_name: model_name,
      input_text: systemPrompt,
      output_text: searchQuery,
      userId: requestData.userId,
      conversationId: requestData.conversationId,
    }),
    prisma.search.create({
      data: {
        creator_id: requestData.userId,
        conversation_id: requestData.conversationId,
        issue_tree_id: requestData.issueTreeId,
        selected_node_id: requestData.selectedNodeId,
        search_query: searchQuery,
        search_result: JSON.stringify(searchResults),
      },
    }),
  ])

  return NextResponse.json({ success: true })
}

async function handleLogAndSaveResponse(data: {
  fullSystemPrompt: string
  result: string
  model_name: string
  requestData: ragAssistantRequestType
}) {
  const { fullSystemPrompt, result, model_name, requestData } = data

  if (!requestData.userId) {
    return NextResponse.json(
      { message: 'Unauthorized. User ID is required.' },
      { status: 401 }
    )
  }

  await Promise.all([
    logOpenAIUsage_serverAction({
      open_ai_usage_type: OpenAIUsageType.RAG_GENERATE_RESPONSE,
      model_name: model_name,
      input_text: fullSystemPrompt,
      output_text: result,
      userId: requestData.userId,
      conversationId: requestData.conversationId,
    }),
    prisma.rAGResponse.create({
      data: {
        creator_id: requestData.userId,
        conversation_id: requestData.conversationId,
        issue_tree_id: requestData.issueTreeId,
        selected_node_id: requestData.selectedNodeId,
        generation_input: fullSystemPrompt,
        generation_output: result,
      },
    }),
  ])

  return NextResponse.json({ success: true })
}
