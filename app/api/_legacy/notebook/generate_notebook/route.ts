import { generateNotebookRequestType } from '@/app/types/api'
import { NextResponse } from 'next/server'
import { getNotebookById, updateNotebook } from '@/app/server-actions/notebook'
import { streamText } from 'ai'
import { azure } from '@ai-sdk/azure'
import { NotebookStatus, OpenAIUsageType } from '@prisma/client'
import { logOpenAIUsage_serverAction } from '@/app/server-actions/log_openai_usage'

export const maxDuration = 60

export async function POST(req: Request) {
  // TODO: Re-implement with AI SDK 5 - disabled for now to unblock core features
  return new Response(
    JSON.stringify({ error: 'Notebook generation temporarily disabled' }),
    {
      status: 503,
      headers: { 'Content-Type': 'application/json' },
    }
  )

  const requestData: generateNotebookRequestType = await req.json()
  console.log('Received request data:', requestData)

  if (
    !requestData.userId ||
    !requestData.notebookId ||
    !requestData.conversationId
  ) {
    return NextResponse.json(
      {
        message:
          'Unauthorized. User ID, notebook ID, and conversation ID are required.',
      },
      { status: 401 }
    )
  }

  const notebook = await getNotebookById(requestData.notebookId)

  if (!notebook) {
    return NextResponse.json(
      { message: 'Notebook not found.' },
      { status: 404 }
    )
  }

  const model_name = 'gpt-4o-mini'

  try {
    const systemPrompt = notebook?.generation_input || ''
    const result = streamText({
      model: azure(model_name),
      prompt: systemPrompt,
      async onFinish(result) {
        await Promise.all([
          updateNotebook(notebook?.id || '', {
            generation_output: result.text,
            status: NotebookStatus.ACTIVE,
          }),
          logOpenAIUsage_serverAction({
            open_ai_usage_type: OpenAIUsageType.NOTEBOOK_GENERATE,
            userId: requestData.userId,
            model_name: model_name,
            input_text: systemPrompt,
            output_text: result.text,
            conversationId: requestData.conversationId,
          }),
        ])
      },
    })
    return result.toUIMessageStreamResponse()
  } catch (error) {
    console.error('Error in getNotebookById:', error)
    return NextResponse.json(
      { message: 'Internal Server Error.' },
      { status: 500 }
    )
  }
}
