// This is to create the new conversation, this time not chat, but issue tree

import getCurrentUser from '@/app/server-actions/getCurrentUser'
import { NextResponse } from 'next/server'

import prisma from '@/app/libs/prismadb'
import { FeedbackType, UserStatus } from '@prisma/client'

export async function POST(request: Request) {
  try {
    const currentUser = await getCurrentUser()
    const body = await request.json()
    const { outputObject, conversationId, canContact } = body

    if (!currentUser?.id || !currentUser?.email) {
      return new NextResponse('Unauthorized', { status: 400 })
    }

    // Block them from accessing API if they are not active
    if (currentUser?.status !== UserStatus.ACTIVE) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // if (Object.keys(outputObject).length === 0) {
    //   return new NextResponse("No object in", {
    //     status: 400,
    //   });
    // }

    const newFeedback = await prisma.feedback.create({
      data: {
        creator_id: currentUser.id,
        conversation_id: conversationId,
        type: FeedbackType.ISSUETREE,
        can_contact: canContact,
        data: outputObject,
      },
    })

    return NextResponse.json(newFeedback)
  } catch {
    return new NextResponse('Internal Error', { status: 500 })
  }
}
