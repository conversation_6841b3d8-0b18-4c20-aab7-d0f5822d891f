import { streamObject } from 'ai'
import { NextResponse } from 'next/server'
import { OpenAIUsageType } from '@prisma/client'
import { azure } from '@ai-sdk/azure'
import { logOpenAIUsage_serverAction } from '@/app/server-actions/log_openai_usage'
import { screenSchema } from './utils'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'

export const maxDuration = 60

export async function POST(req: Request) {
  try {
    const description = await req.json()

    const session = await getServerSession(authOptions)
    const userId = session?.user?.id

    if (!userId) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const model_name = 'gpt-4o-mini'

    const result = streamObject({
      model: azure(model_name),
      schema: screenSchema,
      system: systemMessage,
      prompt: `User Input: ${description}`,
      onFinish: async ({ object }) => {
        await logOpenAIUsage_serverAction({
          open_ai_usage_type: OpenAIUsageType.SCREEN_PROBLEM,
          model_name: model_name,
          input_text: systemMessage + `\nUser Input: ${description}`,
          output_text: JSON.stringify(object),
          userId: userId,
          conversationId: 'dummy_conversation_for_rephrase',
        })
      },
    })

    return result.toTextStreamResponse()
  } catch {
    return NextResponse.json({ message: 'Error' }, { status: 500 })
  }
}

const systemMessage = `
You are an AI assistant designed to screen user inputs for clarity before generating an issue tree. Your goal is to determine if the user's problem statement is clear, specific, and contains sufficient information to proceed.

**Instructions:**

- **Be strict in your analysis.**
- **If the user's main goal or intention is not explicitly stated, you must point it out.**
- Use the attributes as headings in your response.
- For "Specific Entities," interpret any unusual terms.
- For "Ambiguities or Unclear Aspects," **always** point out if the main intention is unclear. If the problem statement is clear, state "No obvious ambiguities."
- For "Pass or No Pass," assign **Pass** only if the score is **5**.
- **Do not infer intentions that are not explicitly stated by the user.**

**Examples:**

**User Input:** I am planning to launch a marketing campaign for Acme Corp's new product line.

1. **Intention**: Launch a marketing campaign for Acme Corp's new product line.
2. **Specific Entities**:
   - Acme Corp [a hypothetical company]
   - New product line
3. **Ambiguities or Unclear Aspects**:
   - No obvious ambiguities.
4. **Overall Quality Score**: 5
5. **Pass or No Pass**: true

**User Input:** I sent an email to my boss without a subject line, and now I'm worried.

1. **Intention**: Not clearly stated.
2. **Specific Entities**:
   - Email
   - Boss
3. **Ambiguities or Unclear Aspects**:
   - The user's main goal or intention is not clear.
4. **Overall Quality Score**: 2
5. **Pass or No Pass**: false
`
