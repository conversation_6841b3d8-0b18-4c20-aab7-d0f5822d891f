'use client'

import React, { useState } from 'react'
import { Session } from 'next-auth'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  FiMail,
  FiCalendar,
  FiCreditCard,
  FiSettings,
  FiLogOut,
  FiStar,
  FiZap,
} from 'react-icons/fi'
import { signOut } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { createStripePortal } from '@/lib/stripe/server'
import { hasPaidFeatures } from '@/app/configs/tier-permissions'
import { SubscriptionTier } from '@prisma/client'
import { useUser } from '@/app/context/UserContext'
import mixpanel from '@/app/libs/mixpanel'

type ProfileDialogProps = {
  isOpen: boolean
  onClose: () => void
  session: Session
}

const ProfileDialog: React.FC<ProfileDialogProps> = ({
  isOpen,
  onClose,
  session,
}) => {
  const router = useRouter()
  const currentUser = useUser()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const user = session.user
  // Note: error is available but not used in this component

  const handleSignOut = async () => {
    setIsLoading(true)
    try {
      await signOut({ redirect: false })
      toast.success('Signed out successfully')
      router.push('/')
      onClose()
    } catch {
      // Error handled by showing toast message - no need to use error object
      toast.error('Error signing out')
    } finally {
      setIsLoading(false)
    }
  }

  const handleManageSubscription = async () => {
    if (!currentUser?.subscription_customer_id) {
      toast.error('No subscription found to manage')
      return
    }

    setIsLoading(true)
    try {
      mixpanel.track('click_manage_subscription_profile')
      const redirectUrl = await createStripePortal(currentUser)

      if (typeof redirectUrl === 'string') {
        toast.success('Redirecting to Stripe...')
        window.open(redirectUrl, '_blank')
      } else if (redirectUrl instanceof Error) {
        toast.error(redirectUrl.message)
      }
    } catch (error) {
      toast.error('Failed to open subscription management')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubscribe = () => {
    mixpanel.track('click_subscribe_profile')
    onClose()
    router.push('/subscription')
  }

  // Real subscription data from session
  const subscriptionInfo = session.user.subscription
  const tier = subscriptionInfo?.tier || SubscriptionTier.FREE
  const isPaidUser = hasPaidFeatures(tier)
  const subscriptionEndDate = subscriptionInfo?.expiry
    ? new Date(subscriptionInfo.expiry).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      })
    : null
  const cancelPending = subscriptionInfo?.cancelPending || false

  // Show raw DB tier value for maximum clarity and alignment with backend

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md mx-auto bg-white rounded-2xl shadow-2xl border-0 p-0 overflow-hidden">
        {/* Header with gradient background */}
        <div className="bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 px-6 py-8 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <DialogHeader>
              <div className="flex items-center space-x-4">
                <div className="relative">
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={user?.image || '/images/placeholder.jpg'}
                    alt="Profile"
                    width={64}
                    height={64}
                    className="w-16 h-16 rounded-full border-4 border-white/30 shadow-lg object-cover"
                  />
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-400 rounded-full border-2 border-white flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>
                <div className="flex-1">
                  <DialogTitle className="text-xl font-bold text-white mb-1">
                    {user?.name || 'User Name'}
                  </DialogTitle>
                  <DialogDescription className="text-blue-100 text-sm">
                    Welcome back to your profile
                  </DialogDescription>
                </div>
              </div>
            </DialogHeader>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-6 space-y-6">
          {/* User Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Account Information
            </h3>

            {/* Email */}
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <FiMail className="w-5 h-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-700">Email</p>
                <p className="text-sm text-gray-900">
                  {user?.email || '<EMAIL>'}
                </p>
              </div>
            </div>

            {/* Subscription Status */}
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <FiStar className="w-5 h-5 text-purple-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-700">
                  Subscription
                </p>
                <div className="flex items-center space-x-2">
                  <p className="text-sm text-gray-900">{tier}</p>
                  <Badge
                    variant={isPaidUser ? 'default' : 'secondary'}
                    className={`text-xs ${
                      isPaidUser
                        ? 'bg-purple-100 text-purple-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {isPaidUser ? 'Active' : 'Free'}
                  </Badge>
                  {cancelPending && (
                    <Badge
                      variant="outline"
                      className="text-xs text-orange-600 border-orange-200"
                    >
                      Ending Soon
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {/* Subscription End Date */}
            {isPaidUser &&
              subscriptionEndDate &&
              tier !== SubscriptionTier.GUEST && (
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <FiCalendar className="w-5 h-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-700">
                      {cancelPending ? 'Expires On' : 'Renews On'}
                    </p>
                    <p className="text-sm text-gray-900">
                      {subscriptionEndDate}
                    </p>
                  </div>
                </div>
              )}

            {/* Guest Access Notice */}
            {tier === SubscriptionTier.GUEST && (
              <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-xl border border-blue-200">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <FiStar className="w-5 h-5 text-blue-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-blue-700">
                    Guest Access
                  </p>
                  <p className="text-sm text-blue-600">
                    You have complimentary access to all Pro features
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="space-y-3 pt-4 border-t border-gray-100">
            {/* Subscription Actions */}
            {isPaidUser && currentUser?.subscription_customer_id ? (
              <Button
                onClick={handleManageSubscription}
                disabled={isLoading}
                variant="outline"
                className="w-full flex items-center justify-center space-x-2 h-12 rounded-xl border-gray-200 hover:bg-gray-50 transition-colors"
              >
                <FiCreditCard className="w-4 h-4" />
                <span>{isLoading ? 'Loading...' : 'Manage Subscription'}</span>
              </Button>
            ) : tier === SubscriptionTier.FREE ||
              tier === SubscriptionTier.GUEST ||
              tier === SubscriptionTier.VIEWER ? (
              <Button
                onClick={handleSubscribe}
                className="w-full flex items-center justify-center space-x-2 h-12 rounded-xl bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white border-0 transition-all"
              >
                <FiZap className="w-4 h-4" />
                <span>Upgrade to Pro</span>
              </Button>
            ) : null}

            {/* Account Settings removed – no content to configure currently */}

            {/* Sign Out */}
            <Button
              onClick={handleSignOut}
              disabled={isLoading}
              variant="outline"
              className="w-full flex items-center justify-center space-x-2 h-12 rounded-xl border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 transition-colors"
            >
              <FiLogOut className="w-4 h-4" />
              <span>{isLoading ? 'Signing out...' : 'Sign Out'}</span>
            </Button>
          </div>

          {/* Footer */}
          <div className="text-center pt-4 border-t border-gray-100">
            <p className="text-xs text-gray-500">
              Member since {new Date().getFullYear()}
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default React.memo(ProfileDialog)
