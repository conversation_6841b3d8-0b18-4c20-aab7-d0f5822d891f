import React from 'react'
import { useSession } from 'next-auth/react'
import { SubscriptionTier } from '@prisma/client'
import { getTierPermissions } from '@/app/configs/tier-permissions'
import UpgradeNotice from '@/app/components/UpgradeNotice'
import BaseButton from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/BaseButton'
import LanguageSelector from '@/app/(conv)/screening/components/LanguageSelector'
import type { SupportedLanguageCode } from '@/app/(conv)/screening/constants/languages'

type ActionButtonsProps = {
  description: string
  isLoading: boolean
  selectedLanguage: SupportedLanguageCode
  canStartClarification: boolean
  hasUserMadeSelection?: boolean
  hasRephrasedQuestions?: boolean
  onParaphraseAndAnalyze: () => void
  onStartClarification: () => void
  onLanguageChange: (language: SupportedLanguageCode) => void
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  description,
  isLoading,
  selectedLanguage,
  canStartClarification,
  hasUserMadeSelection = false,
  hasRephrasedQuestions = false,
  onParaphraseAndAnalyze,
  onStartClarification,
  onLanguageChange,
}) => {
  const minChars = 10
  const isParaphraseDisabled = description.length < minChars || isLoading
  const isClarifyDisabled = !canStartClarification || isLoading

  // Upsell helper: show remaining quota for drag trees
  const { data: session } = useSession()
  const tier = ((session?.user as any)?.subscription_tier ||
    (session?.user as any)?.subscription?.tier ||
    SubscriptionTier.FREE) as SubscriptionTier
  const { maxDragTrees } = getTierPermissions(tier)
  const [existingCount, setExistingCount] = React.useState<number>(0)

  React.useEffect(() => {
    // Lightweight fetch to API route; avoids importing server actions into client components
    const fetchCount = async () => {
      try {
        const res = await fetch('/api/dragtree/count', { cache: 'no-store' })
        if (res.ok) {
          const data = await res.json()
          setExistingCount(Number(data.count) || 0)
        }
      } catch {}
    }
    fetchCount()
  }, [])

  // Determine workflow state - only apply selection requirement when questions are showing
  const needsSelection = hasRephrasedQuestions && !hasUserMadeSelection

  // Gate for VIEWER or when quota is fully used (maxDragTrees <= existingCount)
  const isBlocked =
    tier === SubscriptionTier.VIEWER || existingCount >= maxDragTrees

  return (
    <div className="flex flex-col space-y-3 pt-4">
      {isBlocked && (
        <UpgradeNotice
          message={
            tier === SubscriptionTier.VIEWER
              ? 'Viewer plan cannot create drag trees. Upgrade to use screening.'
              : 'Your plan has reached its drag tree quota. Upgrade for more.'
          }
        />
      )}
      {/* Action Buttons Row with Language Selector */}
      <div className="flex flex-col lg:flex-row gap-3">
        {/* Main Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-2 flex-1">
          {/* Step 1: Paraphrase Button - Only disabled when questions showing and no selection */}
          <BaseButton
            onClick={onParaphraseAndAnalyze}
            disabled={
              isBlocked ||
              isParaphraseDisabled ||
              (needsSelection && !isLoading)
            }
            loadingState={isLoading ? 'loading' : 'idle'}
            loadingText="Paraphrasing..."
            variant="step"
            colorScheme="green"
            stepConfig={{
              stepNumber: 1,
              stepText: 'Paraphrase & Analyze',
              colorScheme: 'green',
            }}
            className="flex-1"
            enableHoverEffects={true}
            enableClickAnimation={true}
          >
            Paraphrase & Analyze
          </BaseButton>

          {/* Step 2: Clarify Button - Disabled until user makes selection */}
          <BaseButton
            onClick={onStartClarification}
            disabled={isBlocked || isClarifyDisabled}
            loadingState="idle"
            variant="step"
            colorScheme="blue"
            stepConfig={{
              stepNumber: 2,
              stepText: 'Start Clarification',
              colorScheme: 'blue',
            }}
            className="flex-1"
            enableHoverEffects={true}
            enableClickAnimation={true}
          >
            Start Clarification
          </BaseButton>
        </div>

        {/* Compact Language Selector */}
        <div className="flex items-center justify-center lg:flex-shrink-0">
          <LanguageSelector
            value={selectedLanguage}
            onChange={onLanguageChange}
            disabled={isLoading || isBlocked}
            compact={true}
          />
        </div>
      </div>

      {/* Status Messages - Simplified */}
      <div className="text-xs space-y-1">
        {description.length < minChars && (
          <p className="text-orange-600 flex items-center space-x-1">
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            <span>Please add more details to enable paraphrasing</span>
          </p>
        )}
        {needsSelection && (
          <p className="text-blue-600 flex items-center space-x-1">
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
            <span>
              Please select a refined statement or use your original input to
              proceed
            </span>
          </p>
        )}
        {tier === SubscriptionTier.FREE &&
          existingCount === maxDragTrees - 1 && (
            <UpgradeNotice message="You can create one more drag tree under Free. Consider upgrading to PRO for higher limits." />
          )}
        {tier === SubscriptionTier.FREE && existingCount >= maxDragTrees && (
          <UpgradeNotice message="You've reached the Free tier limit. Consider upgrading to PRO for higher limits." />
        )}
      </div>
    </div>
  )
}

export default ActionButtons
