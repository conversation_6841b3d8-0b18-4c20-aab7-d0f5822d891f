import { issueTreeStoreStatus } from '@/app/stores/issuetree_store'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'
import { extractTextContent } from '@/app/types/ai-sdk5'
import useSubtreeStore from '@/app/stores/subtree_store'
import { useOnNodeChange } from '@/app/hooks/useOnNodeChange'
import {
  checkIssueTreeIsActive,
  setIssueTreeStatus,
} from '@/app/server-actions'
import { IssueTreeStatus } from '@prisma/client'
import markdownToReactFlow from '@/app/(legacy)/_conversations/[conversationId]/issue_tree/markdownToReactFlow'
import { getDagreLayoutedElements } from '@/app/(legacy)/_conversations/[conversationId]/issue_tree/dagreAutoLayout'
import { addSuffixAfterNumber } from '@/lib/utils'
import { User } from '@prisma/client'
import useIssueTreeStore from '@/app/stores/issuetree_store'
import {
  fetchedIssueTreeType,
  fetchedFeedbacksType,
  fetchedSubtreesType,
} from '@/app/server-actions'
import isEqual from 'lodash.isequal'

export const useIssueTreeLogic = ({
  conversationId,
  currentUser,
  fetchedIssueTree,
  reloadIssueTreeData,
  // fetchedFeedbacks,
  fetchedSubtrees,
}: {
  conversationId: string
  currentUser: User | null
  fetchedIssueTree: fetchedIssueTreeType | null
  reloadIssueTreeData: () => Promise<void>
  // fetchedFeedbacks: fetchedFeedbacksType[];
  fetchedSubtrees: fetchedSubtreesType[]
}) => {
  const subtreeStore = useSubtreeStore()
  const issueTreeStore = useIssueTreeStore()
  const { onNodesChange } = useOnNodeChange()

  const [showSheet, setShowSheet] = useState(false)
  const openSheet = () => {
    setShowSheet(true)
    if (issueTreeStore.nodes.length > 0) {
      issueTreeStore.setTreeMarkdownText()
    }
  }
  const closeSheet = () => setShowSheet(false)

  // onFinish in useChat [client side] is called when streaming is Done, which is not equal to API & data is ready
  // We need to ensure the issue tree status is not IssueTreeStatus.INITIALIZED, otherwise it may start streaming again
  // status will become ACTIVE after the API, hence we check that
  const checkStatusAndReload = useCallback(
    async (retryCount = 0, maxRetries = 3) => {
      try {
        const isActive = await checkIssueTreeIsActive(conversationId)
        if (isActive) {
          reloadIssueTreeData()
        } else if (retryCount < maxRetries) {
          setTimeout(
            () => {
              checkStatusAndReload(retryCount + 1, maxRetries)
            },
            250 * (retryCount + 1)
          )
        } else {
          console.error('Max retries reached, issue tree is not active.')
        }
      } catch (error) {
        console.error('Error checking issue tree status:', error)
        if (retryCount < maxRetries) {
          setTimeout(
            () => {
              checkStatusAndReload(retryCount + 1, maxRetries)
            },
            250 * (retryCount + 1)
          )
        }
      }
    },
    [conversationId, reloadIssueTreeData]
  )

  const { messages, sendMessage, status } = useChat({
    transport: new DefaultChatTransport({
      api: '/api/issuetree/generate_questions',
      body: {
        conversationId,
        currentUser,
        issueTreeId: fetchedIssueTree?.id,
      },
    }),
    onFinish: async () => {
      await checkStatusAndReload()
    },
  })

  // AI SDK 5 compatibility
  const isLoading = status === 'streaming' || status === 'submitted'
  const append = (message: { content: string; role: string }) => {
    if (message.role === 'user') {
      sendMessage({ text: message.content })
    }
  }

  const streamingResponse =
    extractTextContent(messages[messages.length - 1]) || ''
  const rawMarkdown = fetchedIssueTree?.raw_markdown || ''

  const appendRef = useRef(append)
  const hasAppendedRef = useRef(false)

  const appendOnce = useCallback(async (message: any) => {
    if (!hasAppendedRef.current) {
      hasAppendedRef.current = true
      console.log(
        'appendOnce is triggered for the first time',
        hasAppendedRef.current,
        new Date().toISOString()
      )
      await appendRef.current(message)
    } else {
      // This is a weird bug, somehow regardless of how many condition checks we added
      // appendOnce inside generateQuestions is triggered twice => double trigger => double cost
      // rly no clue how to deal with it, hence I tried the idea similar to "singleton pattern"
      // Basically append can only be called once, any subsequent call log the message below
      // We still can't prevent log message below, but at least we prevent another append [hopefully]
      // I have noticed that double triggers happen almost at the same time. I hope someone know React
      // better can help with this, leave as TODO
      console.log(
        'appendOnce is triggered twice, but we prevent it from happening',
        hasAppendedRef.current,
        new Date().toISOString()
      )
    }
  }, [])

  const generateQuestions = async () => {
    if (
      fetchedIssueTree &&
      fetchedIssueTree.status === IssueTreeStatus.INITIALIZED &&
      !isLoading &&
      rawMarkdown.length === 0 &&
      messages.length === 0
    ) {
      // This will change the IssueTreeStatus to GENERATING such that if user refresh the page right away
      // the status is no longer INITIALIZED and will not trigger the API
      // Inside the API, it will change the status to ACTIVE at the end
      try {
        await setIssueTreeStatus(conversationId, IssueTreeStatus.GENERATING)
        await appendOnce({
          content: 'Kickstart the issue tree generation',
          role: 'system',
        })
      } catch (error) {
        console.error('Error generating questions:', error)
        // Reset hasAppendedRef if an error occurs, allowing for a retry
        await setIssueTreeStatus(conversationId, IssueTreeStatus.INITIALIZED)
        hasAppendedRef.current = false
      }
    }
  }

  useEffect(() => {
    if (issueTreeStore.nodes.length > 0 && !isLoading) {
      issueTreeStore.setTreeMarkdownText()
    }

    subtreeStore.resetAllSubtrees()

    const transformedSubtrees = fetchedSubtrees.reduce((acc, subtree) => {
      const { nodes: parsedNodes, edges: parsedEdges } = markdownToReactFlow(
        subtree.generation_output || ''
      )
      const layoutedElements = getDagreLayoutedElements(
        parsedNodes,
        parsedEdges
      )
      acc = {
        ...acc,
        [subtree.selected_node_id]: {
          subtreeId: subtree.id,
          nodes:
            subtree.nodes && JSON.parse(subtree.nodes).length > 0
              ? JSON.parse(subtree.nodes)
              : layoutedElements.nodes,
          edges:
            subtree.edges && JSON.parse(subtree.edges).length > 0
              ? JSON.parse(subtree.edges)
              : layoutedElements.edges,
          generation_output: subtree.generation_output || '',
        },
      }
      return acc
    }, {})

    subtreeStore.setSubtrees(transformedSubtrees)

    if (!hasAppendedRef.current) {
      generateQuestions()
    }
  }, [])

  // streaming [generating Q]
  useEffect(() => {
    // response has something
    if (streamingResponse.length > 0) {
      // Start parse, markdown to react flow [graph]
      const markdownTextToParse = streamingResponse
      const { nodes: initialNodes, edges: initialEdges } = markdownToReactFlow(
        // Add suffix after number to remind user those are AI generated number
        addSuffixAfterNumber(markdownTextToParse, '[EXAMPLE NUMBER]')
      )
      const newLayoutedElements = getDagreLayoutedElements(
        initialNodes,
        initialEdges
      )

      if (!isEqual(issueTreeStore.nodes, newLayoutedElements.nodes)) {
        issueTreeStore.setNodes(
          newLayoutedElements.nodes,
          issueTreeStoreStatus.Ready
        )
      }

      if (!isEqual(issueTreeStore.edges, newLayoutedElements.edges)) {
        issueTreeStore.setEdges(newLayoutedElements.edges)
      }
      // When streaming stops, turn status to ready
      if (!isLoading) {
        issueTreeStore.setStatus(issueTreeStoreStatus.Ready)
        issueTreeStore.setTreeMarkdownText()
      }
    }
  }, [streamingResponse])

  return {
    issueTreeStore,
    subtreeStore,
    showSheet,
    openSheet,
    closeSheet,
    onNodesChange,
    isLoading,
  }
}
