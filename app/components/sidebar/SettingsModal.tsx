'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { User, SubscriptionTier } from '@prisma/client'
import { createStripePortal } from '@/lib/stripe/server'

import Modal from '../modals/Modal'
import Image from 'next/image'
import { toast } from 'react-hot-toast'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { FiZap, FiCreditCard } from 'react-icons/fi'
import mixpanel from '@/app/libs/mixpanel'
import { hasPaidFeatures } from '@/app/configs/tier-permissions'

type SettingsModalProps = {
  isOpen?: boolean
  onClose: () => void
  currentUser: User
}

const SettingsModal: React.FC<SettingsModalProps> = ({
  isOpen,
  onClose,
  currentUser,
}) => {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  // Get subscription info from currentUser (align strictly with DB value)
  const tier = currentUser?.subscription_tier || SubscriptionTier.FREE
  const isPaidUser = hasPaidFeatures(tier)
  const showStripePortal = currentUser?.subscription_customer_id

  const handleStripePortalRequest = async () => {
    if (!currentUser?.subscription_customer_id) {
      toast.error('You need to subscribe first to connect to your portal!')
      return
    } else {
      {
        mixpanel.track('click_manage_subscription')
        setIsLoading(true)
        const redirectUrl = await createStripePortal(currentUser)
        setIsLoading(false)
        if (typeof redirectUrl === 'string') {
          toast.success('Redirecting to Stripe...')
          return router.push(redirectUrl)
        } else if (redirectUrl instanceof Error) {
          toast.error(redirectUrl.message)
        }
      }
    }
  }

  const handleUpgrade = () => {
    mixpanel.track('click_upgrade_settings_modal')
    onClose()
    router.push('/subscription')
  }

  // Display raw DB tier string to avoid mismatch

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="space-y-8">
        <div className="border-b border-gray-900/10 pb-8">
          <h2 className="text-base font-semibold leading-7 text-gray-900">
            Profile
          </h2>

          <div className="mt-6 flex flex-col gap-y-6">
            {/* Name */}
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-bold leading-6 text-gray-900"
              >
                Name
              </label>
              <h4 className="mt-1 text-gray-700">
                {currentUser?.name || '<User Name>'}
              </h4>
            </div>

            {/* Email */}
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-bold leading-6 text-gray-900"
              >
                Email
              </label>
              <h4 className="mt-1 text-gray-700">
                {currentUser?.email || '<User Email>'}
              </h4>
            </div>

            {/* Subscription Tier */}
            <div>
              <label
                htmlFor="tier"
                className="block text-sm font-bold leading-6 text-gray-900"
              >
                Current Plan
              </label>
              <div className="mt-1 flex items-center gap-3">
                <Badge
                  variant={isPaidUser ? 'default' : 'secondary'}
                  className={`text-sm font-semibold ${
                    isPaidUser
                      ? 'bg-purple-100 text-purple-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {tier}
                </Badge>
                {isPaidUser && (
                  <span className="text-xs text-green-600 font-medium">
                    ✓ Premium Features
                  </span>
                )}
              </div>
            </div>
            {/* Photo */}
            <div>
              <label
                htmlFor="photo"
                className="block text-sm font-bold leading-6 text-gray-900"
              >
                Photo
              </label>
              <div className="mt-2 flex items-center gap-x-3">
                <Image
                  width="48"
                  height="48"
                  className="rounded-full"
                  src={currentUser?.image || '/images/placeholder.jpg'}
                  alt="Avatar"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons Section */}
        <div className="space-y-4">
          {/* Subscription Actions */}
          {tier === SubscriptionTier.FREE ||
          tier === SubscriptionTier.GUEST ||
          tier === SubscriptionTier.VIEWER ? (
            <Button
              onClick={handleUpgrade}
              className="w-full flex items-center justify-center space-x-2 h-12 rounded-xl bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white border-0 transition-all"
            >
              <FiZap className="w-4 h-4" />
              <span>Upgrade to Pro</span>
            </Button>
          ) : isPaidUser && showStripePortal ? (
            <Button
              onClick={handleStripePortalRequest}
              disabled={isLoading}
              variant="outline"
              className="w-full flex items-center justify-center space-x-2 h-12 rounded-xl border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <FiCreditCard className="w-4 h-4" />
              <span>{isLoading ? 'Loading...' : 'Manage Subscription'}</span>
            </Button>
          ) : null}

          {/* Subscription End Date Display for Paid Users */}
          {isPaidUser && currentUser?.subscription_end_date && (
            <div className="text-center text-sm text-gray-600">
              <span>
                Subscription{' '}
                {currentUser?.subscription_end_date ? 'renews' : 'ends'} on:{' '}
                {currentUser?.subscription_end_date?.toLocaleDateString(
                  'en-US',
                  {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                  }
                ) || 'N/A'}
              </span>
            </div>
          )}
        </div>
      </div>
    </Modal>
  )
}

export default SettingsModal
