'use client'

import React from 'react'

type UpgradeNoticeProps = {
  message: string
  href?: string
  className?: string
}

export const UpgradeNotice: React.FC<UpgradeNoticeProps> = ({
  message,
  href = '/subscription',
  className,
}) => {
  return (
    <div
      className={
        'rounded-md border border-red-300 bg-red-50 text-red-700 text-sm px-3 py-2 ' +
        (className || '')
      }
      role="alert"
    >
      <div className="flex items-center justify-between gap-3">
        <div className="flex-1">{message}</div>
        <a
          href={href}
          className="shrink-0 inline-flex items-center rounded bg-red-600 px-3 py-1 text-white text-xs font-medium hover:bg-red-700"
        >
          Upgrade
        </a>
      </div>
    </div>
  )
}

export default UpgradeNotice
