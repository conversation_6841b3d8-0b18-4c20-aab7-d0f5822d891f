// LEGACY COMPONENT - COMMENTED OUT FOR V2 MIGRATION
// This component is part of the old conversation-based architecture (v1)
// Replaced by ModernSubscriptionPage in the new dragTree architecture (v2)

// The following code has been commented out as part of the migration to v2:
// - Uses legacy useSubscription hook with Stripe metadata parsing
// - Depends on currentUser context instead of session-based user data
// - Part of the old conversation-based architecture

export const PricingCards = () => {
  return null // Component disabled - use ModernSubscriptionPage instead
}
