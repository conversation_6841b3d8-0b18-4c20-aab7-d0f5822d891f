'use client'

import React, { useState } from 'react'
import { useSession } from 'next-auth/react'
import Sidebar from '@/app/(conv)/dragTree/[dragTreeId]/components/layout/Sidebar'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'

/**
 * Subscription page layout that re-uses the DragTree sidebar behaviour.
 * – Desktop: the sidebar can be toggled with a thin draggable bar identical
 *   to DragTree / Screening pages.
 * – Mobile: the sidebar appears as a sliding drawer with an overlay.
 */
export const UniversalDragTreeLayout: React.FC<{
  children: React.ReactNode
}> = ({ children }) => {
  const { data: session } = useSession()
  // One single piece of state drives visibility across breakpoints.
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false)

  // If user isn’t logged-in (or still loading), just render the content.
  if (!session) {
    return <div className="h-full w-full">{children}</div>
  }

  return (
    <TooltipProvider>
      <div className="h-full flex bg-white relative">
        {/* Sidebar (fixed, slides in/out from the left) */}
        <Sidebar
          isOpen={isSidebarOpen}
          onClose={() => setIsSidebarOpen(false)}
          session={session}
          initialDragTrees={[]}
        />

        {/* Collapse handle – visible only when the sidebar is closed */}
        {!isSidebarOpen && (
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="fixed inset-y-0 left-0 z-[55]">
                <div
                  onClick={() => setIsSidebarOpen(true)}
                  className="w-2 h-full bg-gray-600 hover:bg-gray-700 cursor-pointer group transition-colors"
                >
                  <div className="absolute left-2 top-1/2 -translate-y-1/2 opacity-70 group-hover:opacity-100 transition-opacity">
                    <div className="w-6 h-12 bg-gray-600 rounded-r-xl flex items-center justify-center shadow-lg">
                      <div className="w-1 h-6 bg-white rounded" />
                    </div>
                  </div>
                </div>
              </div>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>Click to open sidebar</p>
            </TooltipContent>
          </Tooltip>
        )}

        {/* Main content area */}
        <main className="flex-1 flex flex-col h-full overflow-auto">
          {/* Mobile header with hamburger */}
          <header className="lg:hidden flex items-center justify-between p-4 border-b border-gray-200 bg-white sticky top-0 z-10">
            <button
              onClick={() => setIsSidebarOpen(true)}
              className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
              aria-label="Open sidebar"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
            <h1 className="text-lg font-semibold text-gray-900">
              Subscription
            </h1>
            <div className="w-10" /> {/* Spacer to keep title centered */}
          </header>

          {/* Actual page content */}
          <div className="flex-1">{children}</div>
        </main>

        {/* Mobile overlay – click to close */}
        {isSidebarOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setIsSidebarOpen(false)}
          />
        )}
      </div>
    </TooltipProvider>
  )
}

// Default export for Next.js layout import convenience.
export default UniversalDragTreeLayout
